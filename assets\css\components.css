/**
 * Component Library for Doctor Portfolio Website
 * Ensures consistency across all pages and components
 */

/* Component Base Styles */
.component {
    transition: all var(--transition);
}

/* Enhanced Page Headers */
.page-header {
    background: var(--primary-gradient);
    color: var(--white);
    padding: var(--spacing-xxl) 0;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: headerGlow 8s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.page-header .container {
    position: relative;
    z-index: 2;
}

/* Enhanced Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-header h2,
.section-header h3 {
    position: relative;
    display: inline-block;
}

.section-header h2::after,
.section-header h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Enhanced Cards */
.enhanced-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition);
    overflow: hidden;
    position: relative;
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition);
}

.enhanced-card:hover::before {
    transform: scaleX(1);
}

.enhanced-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

/* Enhanced Buttons */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn-enhanced:hover::before {
    left: 100%;
}

/* Enhanced Form Controls */
.form-enhanced .form-control,
.form-enhanced .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: all var(--transition);
    background: var(--card-bg);
    color: var(--text-primary);
}

.form-enhanced .form-control:focus,
.form-enhanced .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: scale(1.02);
}

.form-enhanced .form-floating > label {
    color: var(--text-secondary);
    transition: all var(--transition);
}

.form-enhanced .form-floating > .form-control:focus ~ label,
.form-enhanced .form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--primary-color);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Enhanced Navigation */
.nav-enhanced .nav-link {
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all var(--transition);
}

.nav-enhanced .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: all var(--transition);
    transform: translateX(-50%);
}

.nav-enhanced .nav-link:hover::before,
.nav-enhanced .nav-link.active::before {
    width: 80%;
}

/* Enhanced Tables */
.table-enhanced {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-enhanced thead th {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

.table-enhanced tbody tr {
    transition: all var(--transition);
}

.table-enhanced tbody tr:hover {
    background: var(--primary-light);
    transform: scale(1.01);
}

.table-enhanced tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-color: var(--border-color);
    vertical-align: middle;
}

/* Enhanced Modals */
.modal-enhanced .modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    background: var(--card-bg);
}

.modal-enhanced .modal-header {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-enhanced .modal-body {
    padding: var(--spacing-xl);
    color: var(--text-primary);
}

.modal-enhanced .modal-footer {
    border: none;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-secondary);
}

/* Enhanced Alerts */
.alert-enhanced {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-enhanced.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-enhanced.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-enhanced.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-enhanced.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Enhanced Badges */
.badge-enhanced {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.badge-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.badge-enhanced:hover::before {
    left: 100%;
}

/* Enhanced Progress Bars */
.progress-enhanced {
    height: 8px;
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-enhanced .progress-bar {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-enhanced .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%);
    background-size: 20px 20px;
    animation: progressStripes 1s linear infinite;
}

@keyframes progressStripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .enhanced-card {
        margin-bottom: var(--spacing-lg);
    }
    
    .section-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .page-header {
        padding: var(--spacing-lg) 0;
    }
    
    .btn-enhanced {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
}
