/* Enhanced Admin Panel Styles */

:root {
    /* Primary Color Scheme */
    --primary-color: #007BFF;
    --primary-hover: #0056b3;
    --primary-light: rgba(0, 123, 255, 0.1);

    /* Secondary Colors */
    --secondary-color: #17A2B8;
    --secondary-hover: #138496;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;

    /* Background Colors */
    --white: #FFFFFF;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f4f7fc;
    --card-bg: #ffffff;

    /* Text Colors */
    --text-dark: #212529;
    --text-muted: #6C757D;
    --text-light: #ffffff;

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-hover) 100%);

    /* Spacing and Layout */
    --border-radius: 8px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: 0.3s ease-in-out;

    /* Spacing System */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--body-bg);
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--primary-color) 0%, #1e3f73 100%);
    color: white;
    transition: all 0.3s;
    position: relative;
    z-index: 1000;
}

.sidebar.collapsed {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
}

.sidebar.collapsed .sidebar-header h4 {
    display: none;
}

/* Sidebar Navigation */
.sidebar ul.components {
    padding: 1rem 0;
}

.sidebar ul li {
    margin-bottom: 0.5rem;
}

.sidebar ul li a {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    display: block;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar ul li a:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-left-color: var(--secondary-color);
}

.sidebar ul li.active > a {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border-left-color: var(--secondary-color);
}

.sidebar ul li a i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed ul li a {
    padding: 0.75rem;
    text-align: center;
}

.sidebar.collapsed ul li a span:not(.badge) {
    display: none;
}

/* Dropdown */
.sidebar ul li a.dropdown-toggle::after {
    content: '\F282';
    font-family: 'bootstrap-icons';
    float: right;
    transition: transform 0.3s;
}

.sidebar ul li a[aria-expanded="true"]::after {
    transform: rotate(90deg);
}

.sidebar ul ul {
    background: rgba(0, 0, 0, 0.1);
}

.sidebar ul ul li a {
    padding-left: 3rem;
    font-size: 0.9rem;
}

.sidebar.collapsed ul ul {
    display: none !important;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .sidebar-footer .user-info > div > div {
    display: none;
}

.sidebar.collapsed .sidebar-footer .avatar {
    text-align: center;
}

/* Content */
.content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s;
    background-color: #f4f6f9;
}

/* Top Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#sidebarCollapse {
    border: none;
    background: transparent;
    color: var(--primary-color);
}

#sidebarCollapse:hover {
    background: var(--light-color);
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '>';
    color: #6c757d;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card.success {
    background: linear-gradient(135deg, var(--secondary-color), #20c997);
}

.stats-card.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stats-card.danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.stats-card .icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-card .number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1e3f73;
    border-color: #1e3f73;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.badge-sm {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
}

/* Status Badges */
.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-approved {
    background-color: var(--secondary-color);
}

.status-completed {
    background-color: var(--primary-color);
}

.status-cancelled {
    background-color: #dc3545;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
}

/* Dropdown */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
    background-color: var(--light-color);
}

/* Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: var(--light-color);
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(44, 90, 160, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--secondary-color);
    background-color: rgba(40, 167, 69, 0.05);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -var(--sidebar-width);
    }
    
    .sidebar.active {
        margin-left: 0;
    }
    
    .content {
        width: 100%;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    .sidebar-overlay.active {
        display: block;
    }
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
