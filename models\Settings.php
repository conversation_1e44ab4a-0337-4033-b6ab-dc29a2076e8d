<?php
/**
 * Settings Model
 * Handles website settings and configuration
 */

class Settings {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get all settings
     */
    public function getAllSettings() {
        $sql = "SELECT * FROM settings ORDER BY setting_key";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        $settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * Get setting by key
     */
    public function getSetting($key, $default = null) {
        $sql = "SELECT setting_value FROM settings WHERE setting_key = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$key]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['setting_value'] : $default;
    }
    
    /**
     * Update or create setting
     */
    public function updateSetting($key, $value, $description = '') {
        $sql = "INSERT INTO settings (setting_key, setting_value, description, updated_at) 
                VALUES (?, ?, ?, CURRENT_TIMESTAMP) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                description = VALUES(description), 
                updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$key, $value, $description]);
    }
    
    /**
     * Delete setting
     */
    public function deleteSetting($key) {
        $sql = "DELETE FROM settings WHERE setting_key = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$key]);
    }
    
    /**
     * Get settings by category
     */
    public function getSettingsByCategory($category) {
        $sql = "SELECT * FROM settings WHERE setting_key LIKE ? ORDER BY setting_key";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category . '%']);
        
        $settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * Bulk update settings
     */
    public function bulkUpdateSettings($settings) {
        $this->db->beginTransaction();
        
        try {
            foreach ($settings as $key => $value) {
                $this->updateSetting($key, $value);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get default settings structure
     */
    public function getDefaultSettings() {
        return [
            // Site Information
            'site_title' => 'Doctor Portfolio',
            'site_description' => 'Professional medical services and healthcare consultation',
            'site_keywords' => 'doctor, medical, healthcare, consultation',
            'site_logo' => '',
            'site_favicon' => '',
            
            // Contact Information
            'contact_email' => '',
            'contact_phone' => '',
            'contact_address' => '',
            'contact_whatsapp' => '',
            
            // Social Media
            'social_facebook' => '',
            'social_twitter' => '',
            'social_instagram' => '',
            'social_linkedin' => '',
            'social_youtube' => '',
            
            // SEO Settings
            'meta_title' => '',
            'meta_description' => '',
            'meta_keywords' => '',
            'google_analytics' => '',
            'google_maps_embed' => '',
            
            // Email Settings
            'smtp_host' => '',
            'smtp_port' => '587',
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls',
            'email_from_name' => '',
            'email_from_address' => '',
            
            // Appointment Settings
            'appointment_duration' => '30',
            'appointment_buffer' => '15',
            'max_advance_booking' => '30',
            'appointment_confirmation_email' => '1',
            'appointment_reminder_email' => '1',
            
            // Website Settings
            'maintenance_mode' => '0',
            'user_registration' => '0',
            'comment_moderation' => '1',
            'timezone' => 'Asia/Dhaka',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            
            // Theme Settings
            'theme_primary_color' => '#007BFF',
            'theme_secondary_color' => '#17A2B8',
            'theme_default_mode' => 'light',
            
            // Security Settings
            'session_timeout' => '3600',
            'max_login_attempts' => '5',
            'login_lockout_time' => '900',
            'password_min_length' => '8',
            
            // File Upload Settings
            'max_file_size' => '5242880', // 5MB
            'allowed_file_types' => 'jpg,jpeg,png,gif,pdf,doc,docx',
            'upload_path' => 'uploads/',
            
            // Cache Settings
            'cache_enabled' => '1',
            'cache_duration' => '3600',
            
            // Backup Settings
            'auto_backup' => '0',
            'backup_frequency' => 'weekly',
            'backup_retention' => '30'
        ];
    }
    
    /**
     * Initialize default settings
     */
    public function initializeDefaultSettings() {
        $defaultSettings = $this->getDefaultSettings();
        
        foreach ($defaultSettings as $key => $value) {
            // Only add if setting doesn't exist
            $existing = $this->getSetting($key);
            if ($existing === null) {
                $this->updateSetting($key, $value);
            }
        }
        
        return true;
    }
    
    /**
     * Export settings
     */
    public function exportSettings() {
        $settings = $this->getAllSettings();
        return json_encode($settings, JSON_PRETTY_PRINT);
    }
    
    /**
     * Import settings
     */
    public function importSettings($jsonData) {
        $settings = json_decode($jsonData, true);
        
        if (!$settings) {
            return false;
        }
        
        return $this->bulkUpdateSettings($settings);
    }
    
    /**
     * Reset settings to default
     */
    public function resetToDefault() {
        // Clear existing settings
        $sql = "DELETE FROM settings";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        // Initialize default settings
        return $this->initializeDefaultSettings();
    }
    
    /**
     * Get settings for specific sections
     */
    public function getSettingsSections() {
        return [
            'site' => 'Site Information',
            'contact' => 'Contact Information',
            'social' => 'Social Media',
            'seo' => 'SEO Settings',
            'email' => 'Email Settings',
            'appointment' => 'Appointment Settings',
            'theme' => 'Theme Settings',
            'security' => 'Security Settings',
            'upload' => 'File Upload Settings',
            'cache' => 'Cache Settings',
            'backup' => 'Backup Settings'
        ];
    }
    
    /**
     * Validate setting value
     */
    public function validateSetting($key, $value) {
        $validations = [
            'contact_email' => 'email',
            'smtp_port' => 'numeric',
            'appointment_duration' => 'numeric',
            'max_advance_booking' => 'numeric',
            'session_timeout' => 'numeric',
            'max_login_attempts' => 'numeric',
            'password_min_length' => 'numeric',
            'max_file_size' => 'numeric'
        ];
        
        if (!isset($validations[$key])) {
            return true; // No validation rule
        }
        
        $rule = $validations[$key];
        
        switch ($rule) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'numeric':
                return is_numeric($value) && $value >= 0;
            default:
                return true;
        }
    }
}
