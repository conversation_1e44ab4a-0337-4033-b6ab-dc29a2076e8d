<?php
/**
 * Message Model
 * Handles contact messages and communications
 */

class Message {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get all messages
     */
    public function getAllMessages($limit = null, $offset = 0) {
        $sql = "SELECT * FROM contact_messages ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get message by ID
     */
    public function getMessageById($id) {
        $sql = "SELECT * FROM contact_messages WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new message
     */
    public function createMessage($data) {
        $sql = "INSERT INTO contact_messages (name, email, phone, subject, message)
                VALUES (?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['name'],
            $data['email'],
            $data['phone'] ?? null,
            $data['subject'] ?? '',
            $data['message']
        ]);
    }

    /**
     * Update message status
     */
    public function updateMessageStatus($id, $status) {
        // Note: contact_messages table uses is_read instead of status
        $isRead = ($status === 'read' || $status === 'replied') ? 1 : 0;
        $sql = "UPDATE contact_messages SET is_read = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$isRead, $id]);
    }
    
    /**
     * Mark message as read
     */
    public function markAsRead($id) {
        return $this->updateMessageStatus($id, 'read');
    }
    
    /**
     * Mark message as replied
     */
    public function markAsReplied($id) {
        return $this->updateMessageStatus($id, 'replied');
    }
    
    /**
     * Delete message
     */
    public function deleteMessage($id) {
        $sql = "DELETE FROM contact_messages WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }

    /**
     * Get unread messages count
     */
    public function getUnreadCount() {
        $sql = "SELECT COUNT(*) FROM contact_messages WHERE is_read = 0";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    /**
     * Get messages by status
     */
    public function getMessagesByStatus($status, $limit = null) {
        $isRead = ($status === 'read' || $status === 'replied') ? 1 : 0;
        $sql = "SELECT * FROM contact_messages WHERE is_read = ? ORDER BY created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$isRead]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get recent messages
     */
    public function getRecentMessages($limit = 5) {
        $sql = "SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT " . intval($limit);
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Search messages
     */
    public function searchMessages($query, $limit = 20) {
        $sql = "SELECT * FROM contact_messages
                WHERE name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?
                ORDER BY created_at DESC
                LIMIT " . intval($limit);

        $searchTerm = '%' . $query . '%';
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get message statistics
     */
    public function getMessageStats() {
        $sql = "SELECT
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_messages,
                    COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_messages,
                    0 as replied_messages,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_messages
                FROM contact_messages";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get messages by date range
     */
    public function getMessagesByDateRange($startDate, $endDate) {
        $sql = "SELECT * FROM messages 
                WHERE DATE(created_at) BETWEEN ? AND ? 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Add reply to message
     */
    public function addReply($messageId, $reply, $adminId) {
        $sql = "INSERT INTO message_replies (message_id, reply, admin_id) VALUES (?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        
        if ($stmt->execute([$messageId, $reply, $adminId])) {
            // Mark original message as replied
            $this->markAsReplied($messageId);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get replies for a message
     */
    public function getMessageReplies($messageId) {
        $sql = "SELECT mr.*, u.full_name as admin_name 
                FROM message_replies mr 
                LEFT JOIN users u ON mr.admin_id = u.id 
                WHERE mr.message_id = ? 
                ORDER BY mr.created_at ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$messageId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Bulk update message status
     */
    public function bulkUpdateStatus($messageIds, $status) {
        if (empty($messageIds)) {
            return false;
        }
        
        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        $sql = "UPDATE messages SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN ($placeholders)";
        
        $params = array_merge([$status], $messageIds);
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Bulk delete messages
     */
    public function bulkDeleteMessages($messageIds) {
        if (empty($messageIds)) {
            return false;
        }
        
        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        $sql = "DELETE FROM messages WHERE id IN ($placeholders)";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($messageIds);
    }
}
