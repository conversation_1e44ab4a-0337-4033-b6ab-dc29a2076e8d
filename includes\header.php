<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo getSetting('site_description', 'Professional medical services and healthcare consultation with experienced doctors'); ?>">
    <meta name="keywords" content="doctor, medical, healthcare, consultation, appointment, <?php echo htmlspecialchars($doctorProfile['specialization'] ?? 'medical specialist'); ?>, clinic, health">
    <meta name="author" content="<?php echo getSetting('site_title', 'Doctor Portfolio'); ?>">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo getSetting('site_title', 'Doctor Portfolio'); ?>">
    <meta property="og:description" content="<?php echo getSetting('site_description', 'Professional medical services and healthcare consultation'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo BASE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>assets/images/og-image.jpg">
    <meta property="og:site_name" content="<?php echo getSetting('site_title', 'Doctor Portfolio'); ?>">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo getSetting('site_title', 'Doctor Portfolio'); ?>">
    <meta name="twitter:description" content="<?php echo getSetting('site_description', 'Professional medical services and healthcare consultation'); ?>">
    <meta name="twitter:image" content="<?php echo BASE_URL; ?>assets/images/og-image.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo BASE_URL . $_SERVER['REQUEST_URI']; ?>">

    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo getSetting('site_title', 'Doctor Portfolio'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Poppins, Roboto, Lato -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Lato:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- AOS (Animate On Scroll) CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/components.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary-custom fw-bold" href="index.php">
                <i class="bi bi-heart-pulse me-2"></i>
                <?php echo getSetting('site_title', 'Doctor Portfolio'); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo isActivePage('index.php'); ?>" href="index.php">
                            <i class="bi bi-house me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo isActivePage('appointment.php'); ?>" href="appointment.php">
                            <i class="bi bi-calendar-check me-1"></i>Book Appointment
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo isActivePage('contact.php'); ?>" href="contact.php">
                            <i class="bi bi-envelope me-1"></i>Contact
                        </a>
                    </li>
                    <li class="nav-item d-flex align-items-center">
                        <div class="theme-toggle" id="themeToggle" title="Toggle Dark/Light Mode" role="button" tabindex="0" aria-label="Toggle theme">
                            <i class="fas fa-sun theme-toggle-icon sun-icon"></i>
                            <i class="fas fa-moon theme-toggle-icon moon-icon"></i>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        <?php displayFlashMessage(); ?>
    </div>
