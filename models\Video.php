<?php
/**
 * Video Model
 * Handles video content management
 */

class Video {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get all active videos
     */
    public function getAllVideos($limit = null) {
        $sql = "SELECT v.*, u.full_name as created_by_name 
                FROM videos v 
                LEFT JOIN users u ON v.created_by = u.id 
                WHERE v.is_active = 1 
                ORDER BY v.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get videos by category
     */
    public function getVideosByCategory($category, $limit = null) {
        $sql = "SELECT v.*, u.full_name as created_by_name 
                FROM videos v 
                LEFT JOIN users u ON v.created_by = u.id 
                WHERE v.is_active = 1 AND v.category = ? 
                ORDER BY v.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get video by ID
     */
    public function getVideoById($id) {
        $sql = "SELECT v.*, u.full_name as created_by_name 
                FROM videos v 
                LEFT JOIN users u ON v.created_by = u.id 
                WHERE v.id = ? AND v.is_active = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get featured videos for homepage
     */
    public function getFeaturedVideos($limit = 3) {
        $sql = "SELECT v.*, u.full_name as created_by_name 
                FROM videos v 
                LEFT JOIN users u ON v.created_by = u.id 
                WHERE v.is_active = 1 
                ORDER BY v.created_at DESC 
                LIMIT " . intval($limit);
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create new video
     */
    public function createVideo($data) {
        $sql = "INSERT INTO videos (title, description, video_url, video_file, thumbnail, category, tags, duration, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['title'],
            $data['description'],
            $data['video_url'] ?? null,
            $data['video_file'] ?? null,
            $data['thumbnail'] ?? null,
            $data['category'],
            $data['tags'],
            $data['duration'] ?? null,
            $data['created_by']
        ]);
    }
    
    /**
     * Update video
     */
    public function updateVideo($id, $data) {
        $sql = "UPDATE videos SET 
                title = ?, description = ?, video_url = ?, video_file = ?, 
                thumbnail = ?, category = ?, tags = ?, duration = ?, 
                updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['title'],
            $data['description'],
            $data['video_url'] ?? null,
            $data['video_file'] ?? null,
            $data['thumbnail'] ?? null,
            $data['category'],
            $data['tags'],
            $data['duration'] ?? null,
            $id
        ]);
    }
    
    /**
     * Delete video
     */
    public function deleteVideo($id) {
        $sql = "UPDATE videos SET is_active = 0 WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    /**
     * Get video categories
     */
    public function getCategories() {
        $sql = "SELECT DISTINCT category FROM videos WHERE is_active = 1 AND category IS NOT NULL ORDER BY category";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Extract YouTube video ID from URL
     */
    public function extractYouTubeId($url) {
        $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
        preg_match($pattern, $url, $matches);
        return isset($matches[1]) ? $matches[1] : false;
    }
    
    /**
     * Get YouTube thumbnail URL
     */
    public function getYouTubeThumbnail($videoId, $quality = 'maxresdefault') {
        return "https://img.youtube.com/vi/{$videoId}/{$quality}.jpg";
    }
    
    /**
     * Validate video URL
     */
    public function validateVideoUrl($url) {
        // Support YouTube, Vimeo, and direct video files
        $patterns = [
            '/^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\//',
            '/^https?:\/\/(www\.)?vimeo\.com\//',
            '/^https?:\/\/.*\.(mp4|avi|mov|wmv|webm)$/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get video statistics
     */
    public function getVideoStats() {
        $sql = "SELECT 
                    COUNT(*) as total_videos,
                    COUNT(DISTINCT category) as total_categories,
                    AVG(duration) as avg_duration
                FROM videos 
                WHERE is_active = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
