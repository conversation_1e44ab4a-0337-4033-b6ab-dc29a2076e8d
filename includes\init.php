<?php
/**
 * Application Initialization
 * This file should be included at the top of every PHP file
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include configuration
require_once __DIR__ . '/../config/database.php';

// Include core classes
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Security.php';
require_once __DIR__ . '/functions.php';

// Create upload directories if they don't exist
$uploadDirs = [
    UPLOAD_DIR,
    PROFILE_UPLOAD_DIR,
    VIDEO_UPLOAD_DIR,
    ARTICLE_UPLOAD_DIR,
    'logs/'
];

foreach ($uploadDirs as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        
        // Create .htaccess to prevent direct access to uploads
        if (strpos($dir, 'uploads/') !== false) {
            file_put_contents($dir . '.htaccess', "Options -Indexes\nDeny from all");
        }
    }
}

// Set error handling
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorLog = date('Y-m-d H:i:s') . " - Error: {$message} in {$file} on line {$line}\n";
    error_log($errorLog, 3, 'logs/error.log');
    
    // In production, don't display errors
    if (ini_get('display_errors')) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
    
    return true;
});

// Check session timeout
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
    session_unset();
    session_destroy();
    session_start();
}
$_SESSION['last_activity'] = time();

// Initialize database connection (test connection)
try {
    Database::getInstance();
} catch (Exception $e) {
    // Log the error
    error_log("Database initialization failed: " . $e->getMessage());
    
    // In production, show a generic error page
    if (!ini_get('display_errors')) {
        include 'error_pages/database_error.html';
        exit;
    }
}
?>
