<?php
/**
 * Database Configuration
 * Doctor <PERSON>lio Website
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'doctor_portfolio');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application configuration
define('APP_NAME', 'Doctor Portfolio');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/Dcmdlylatulkadir/');
define('ADMIN_URL', BASE_URL . 'admin/');

// Security configuration
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('MAX_FILE_SIZE', 10485760); // 10MB in bytes
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv']);

// Upload directories
define('UPLOAD_DIR', 'uploads/');
define('PROFILE_UPLOAD_DIR', UPLOAD_DIR . 'profiles/');
define('VIDEO_UPLOAD_DIR', UPLOAD_DIR . 'videos/');
define('ARTICLE_UPLOAD_DIR', UPLOAD_DIR . 'articles/');

// Email configuration (optional)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Doctor Portfolio');

// Timezone
date_default_timezone_set('Asia/Dhaka');

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
