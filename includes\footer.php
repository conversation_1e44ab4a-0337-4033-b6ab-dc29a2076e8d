    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="bi bi-heart-pulse me-2"></i><?php echo getSetting('site_title', 'Doctor Portfolio'); ?></h5>
                    <p class="mb-3"><?php echo getSetting('site_description', 'Professional medical services and healthcare consultation with years of experience in providing quality healthcare.'); ?></p>
                    <div class="d-flex">
                        <a href="#" class="text-light me-3"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-linkedin"></i></a>
                        <a href="#" class="text-light"><i class="bi bi-instagram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php">Home</a></li>
                        <li><a href="appointment.php">Book Appointment</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                        <li><a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h5>Contact Info</h5>
                    <?php 
                    $doctorProfile = (new DoctorProfile())->getActiveProfile();
                    if ($doctorProfile): 
                    ?>
                    <div class="contact-info-footer">
                        <p><i class="bi bi-geo-alt me-2"></i><?php echo nl2br(htmlspecialchars($doctorProfile['clinic_address'])); ?></p>
                        <p><i class="bi bi-telephone me-2"></i><?php echo formatPhone($doctorProfile['phone']); ?></p>
                        <p><i class="bi bi-envelope me-2"></i><?php echo htmlspecialchars($doctorProfile['email']); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo getSetting('site_title', 'Doctor Portfolio'); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Designed with <i class="bi bi-heart-fill text-danger"></i> for better healthcare</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Floating Button -->
    <?php if ($doctorProfile && $doctorProfile['whatsapp']): ?>
    <a href="<?php echo generateWhatsAppURL($doctorProfile['whatsapp'], getSetting('whatsapp_message', 'Hello, I would like to schedule an appointment.')); ?>" 
       class="whatsapp-btn" target="_blank" title="Chat on WhatsApp">
        <i class="bi bi-whatsapp"></i>
    </a>
    <?php endif; ?>

    <!-- Privacy Policy Modal -->
    <div class="modal fade" id="privacyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Privacy Policy</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>Information We Collect</h6>
                    <p>We collect information you provide directly to us, such as when you book an appointment or contact us.</p>
                    
                    <h6>How We Use Your Information</h6>
                    <p>We use the information we collect to provide, maintain, and improve our services, including appointment scheduling and communication.</p>
                    
                    <h6>Information Sharing</h6>
                    <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
                    
                    <h6>Data Security</h6>
                    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                    
                    <h6>Contact Us</h6>
                    <p>If you have any questions about this Privacy Policy, please contact us using the information provided on our contact page.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS (Animate On Scroll) JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>

    <!-- Development Validation Script -->
    <?php if (in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', 'localhost:8000'])): ?>
    <script src="assets/js/validation.js"></script>
    <?php endif; ?>
    
    <!-- Additional JS for specific pages -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript -->
    <?php if (isset($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>

    <!-- WhatsApp Floating Button -->
    <?php if ($doctorProfile && $doctorProfile['whatsapp']): ?>
    <div class="whatsapp-float">
        <a href="<?php echo generateWhatsAppURL($doctorProfile['whatsapp'], 'Hello! I would like to get in touch regarding medical consultation.'); ?>"
           target="_blank"
           class="whatsapp-btn"
           aria-label="Contact us on WhatsApp">
            <i class="fab fa-whatsapp"></i>
            <span class="whatsapp-text">Chat with us</span>
        </a>
    </div>

    <style>
    .whatsapp-float {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .whatsapp-btn {
        display: flex;
        align-items: center;
        background: #25d366;
        color: white;
        padding: 12px 20px;
        border-radius: 50px;
        text-decoration: none;
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .whatsapp-btn:hover {
        background: #128c7e;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
    }

    .whatsapp-btn i {
        font-size: 1.5rem;
        margin-right: 8px;
    }

    .whatsapp-text {
        display: none;
    }

    .whatsapp-btn:hover .whatsapp-text {
        display: inline;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-10px); }
        to { opacity: 1; transform: translateX(0); }
    }

    @media (max-width: 768px) {
        .whatsapp-float {
            bottom: 15px;
            right: 15px;
        }

        .whatsapp-btn {
            width: 60px;
            height: 60px;
            padding: 0;
            justify-content: center;
            border-radius: 50%;
        }

        .whatsapp-btn i {
            margin-right: 0;
            font-size: 1.8rem;
        }

        .whatsapp-text {
            display: none !important;
        }
    }
    </style>
    <?php endif; ?>
</body>
</html>
