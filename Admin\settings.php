<?php
require_once '../includes/init.php';
require_once '../models/Settings.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'Website Settings';
$settingsModel = new Settings();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!Security::validateCSRFToken($_POST['csrf_token'])) {
        $_SESSION['flash_message'] = 'Invalid security token.';
        $_SESSION['flash_type'] = 'danger';
        header('Location: settings.php');
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_settings') {
        $settings = $_POST['settings'] ?? [];
        
        // Validate settings
        $errors = [];
        foreach ($settings as $key => $value) {
            if (!$settingsModel->validateSetting($key, $value)) {
                $errors[] = "Invalid value for setting: $key";
            }
        }
        
        if (empty($errors)) {
            if ($settingsModel->bulkUpdateSettings($settings)) {
                $_SESSION['flash_message'] = 'Settings updated successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to update settings.';
                $_SESSION['flash_type'] = 'danger';
            }
        } else {
            $_SESSION['flash_message'] = implode('<br>', $errors);
            $_SESSION['flash_type'] = 'danger';
        }
    } elseif ($action === 'reset_settings') {
        if ($settingsModel->resetToDefault()) {
            $_SESSION['flash_message'] = 'Settings reset to default values.';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'Failed to reset settings.';
            $_SESSION['flash_type'] = 'danger';
        }
    } elseif ($action === 'export_settings') {
        $exportData = $settingsModel->exportSettings();
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="website_settings_' . date('Y-m-d') . '.json"');
        echo $exportData;
        exit;
    } elseif ($action === 'import_settings') {
        if (isset($_FILES['settings_file']) && $_FILES['settings_file']['error'] === UPLOAD_ERR_OK) {
            $jsonData = file_get_contents($_FILES['settings_file']['tmp_name']);
            
            if ($settingsModel->importSettings($jsonData)) {
                $_SESSION['flash_message'] = 'Settings imported successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to import settings. Please check the file format.';
                $_SESSION['flash_type'] = 'danger';
            }
        } else {
            $_SESSION['flash_message'] = 'Please select a valid settings file.';
            $_SESSION['flash_type'] = 'danger';
        }
    }
    
    header('Location: settings.php');
    exit;
}

// Get current settings
$currentSettings = $settingsModel->getAllSettings();
$settingSections = $settingsModel->getSettingsSections();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>Website Settings</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-success" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="fas fa-upload me-2"></i>Import
                    </button>
                    <button class="btn btn-outline-warning" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>Reset to Default
                    </button>
                </div>
            </div>
            
            <form method="POST" id="settingsForm">
                <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update_settings">
                
                <!-- Settings Tabs -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="site-tab" data-bs-toggle="tab" data-bs-target="#site" type="button" role="tab">
                                    Site Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                                    Contact Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="appointment-tab" data-bs-toggle="tab" data-bs-target="#appointment" type="button" role="tab">
                                    Appointment Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                                    Email Settings
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                    Security Settings
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="settingsTabContent">
                            <!-- Site Information Tab -->
                            <div class="tab-pane fade show active" id="site" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_title" class="form-label">Site Title</label>
                                            <input type="text" class="form-control" name="settings[site_title]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['site_title'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="site_description" class="form-label">Site Description</label>
                                            <textarea class="form-control" name="settings[site_description]" rows="3"><?php echo htmlspecialchars($currentSettings['site_description'] ?? ''); ?></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="site_keywords" class="form-label">Site Keywords</label>
                                            <input type="text" class="form-control" name="settings[site_keywords]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['site_keywords'] ?? ''); ?>"
                                                   placeholder="keyword1, keyword2, keyword3">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">Timezone</label>
                                            <select class="form-select" name="settings[timezone]">
                                                <option value="Asia/Dhaka" <?php echo ($currentSettings['timezone'] ?? '') === 'Asia/Dhaka' ? 'selected' : ''; ?>>Asia/Dhaka</option>
                                                <option value="UTC" <?php echo ($currentSettings['timezone'] ?? '') === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                                <option value="America/New_York" <?php echo ($currentSettings['timezone'] ?? '') === 'America/New_York' ? 'selected' : ''; ?>>America/New_York</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="date_format" class="form-label">Date Format</label>
                                            <select class="form-select" name="settings[date_format]">
                                                <option value="Y-m-d" <?php echo ($currentSettings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                                <option value="d/m/Y" <?php echo ($currentSettings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                                <option value="m/d/Y" <?php echo ($currentSettings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="time_format" class="form-label">Time Format</label>
                                            <select class="form-select" name="settings[time_format]">
                                                <option value="H:i" <?php echo ($currentSettings['time_format'] ?? '') === 'H:i' ? 'selected' : ''; ?>>24 Hour (HH:MM)</option>
                                                <option value="g:i A" <?php echo ($currentSettings['time_format'] ?? '') === 'g:i A' ? 'selected' : ''; ?>>12 Hour (H:MM AM/PM)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information Tab -->
                            <div class="tab-pane fade" id="contact" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_email" class="form-label">Contact Email</label>
                                            <input type="email" class="form-control" name="settings[contact_email]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_email'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="contact_phone" class="form-label">Contact Phone</label>
                                            <input type="tel" class="form-control" name="settings[contact_phone]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_phone'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="contact_whatsapp" class="form-label">WhatsApp Number</label>
                                            <input type="tel" class="form-control" name="settings[contact_whatsapp]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_whatsapp'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_address" class="form-label">Contact Address</label>
                                            <textarea class="form-control" name="settings[contact_address]" rows="4"><?php echo htmlspecialchars($currentSettings['contact_address'] ?? ''); ?></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="google_maps_embed" class="form-label">Google Maps Embed Code</label>
                                            <textarea class="form-control" name="settings[google_maps_embed]" rows="3" placeholder="<iframe src=...></iframe>"><?php echo htmlspecialchars($currentSettings['google_maps_embed'] ?? ''); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Appointment Settings Tab -->
                            <div class="tab-pane fade" id="appointment" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="appointment_duration" class="form-label">Default Appointment Duration (minutes)</label>
                                            <input type="number" class="form-control" name="settings[appointment_duration]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['appointment_duration'] ?? '30'); ?>" min="15" max="120">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="appointment_buffer" class="form-label">Buffer Time Between Appointments (minutes)</label>
                                            <input type="number" class="form-control" name="settings[appointment_buffer]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['appointment_buffer'] ?? '15'); ?>" min="0" max="60">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="max_advance_booking" class="form-label">Maximum Advance Booking (days)</label>
                                            <input type="number" class="form-control" name="settings[max_advance_booking]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['max_advance_booking'] ?? '30'); ?>" min="1" max="365">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="settings[appointment_confirmation_email]" 
                                                       value="1" <?php echo ($currentSettings['appointment_confirmation_email'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label">Send Confirmation Email</label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="settings[appointment_reminder_email]" 
                                                       value="1" <?php echo ($currentSettings['appointment_reminder_email'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label">Send Reminder Email</label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="whatsapp_message" class="form-label">Default WhatsApp Message</label>
                                            <textarea class="form-control" name="settings[whatsapp_message]" rows="3"><?php echo htmlspecialchars($currentSettings['whatsapp_message'] ?? 'Hello, I would like to schedule an appointment.'); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Email Settings Tab -->
                            <div class="tab-pane fade" id="email" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtp_host" class="form-label">SMTP Host</label>
                                            <input type="text" class="form-control" name="settings[smtp_host]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_host'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="smtp_port" class="form-label">SMTP Port</label>
                                            <input type="number" class="form-control" name="settings[smtp_port]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_port'] ?? '587'); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="smtp_username" class="form-label">SMTP Username</label>
                                            <input type="text" class="form-control" name="settings[smtp_username]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_username'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="smtp_password" class="form-label">SMTP Password</label>
                                            <input type="password" class="form-control" name="settings[smtp_password]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_password'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="email_from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" name="settings[email_from_name]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['email_from_name'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="email_from_address" class="form-label">From Email Address</label>
                                            <input type="email" class="form-control" name="settings[email_from_address]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['email_from_address'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Security Settings Tab -->
                            <div class="tab-pane fade" id="security" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                            <input type="number" class="form-control" name="settings[session_timeout]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['session_timeout'] ?? '3600'); ?>" min="300">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                                            <input type="number" class="form-control" name="settings[max_login_attempts]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['max_login_attempts'] ?? '5'); ?>" min="3" max="10">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="login_lockout_time" class="form-label">Login Lockout Time (seconds)</label>
                                            <input type="number" class="form-control" name="settings[login_lockout_time]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['login_lockout_time'] ?? '900'); ?>" min="300">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_min_length" class="form-label">Minimum Password Length</label>
                                            <input type="number" class="form-control" name="settings[password_min_length]" 
                                                   value="<?php echo htmlspecialchars($currentSettings['password_min_length'] ?? '8'); ?>" min="6" max="20">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="settings[maintenance_mode]" 
                                                       value="1" <?php echo ($currentSettings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label">Maintenance Mode</label>
                                            </div>
                                            <div class="form-text">Enable to show maintenance page to visitors</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="import_settings">
                    
                    <div class="mb-3">
                        <label for="settings_file" class="form-label">Settings File (JSON)</label>
                        <input type="file" class="form-control" name="settings_file" accept=".json" required>
                        <div class="form-text">Select a JSON file exported from this system.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Import Settings</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function exportSettings() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
        <input type="hidden" name="action" value="export_settings">
    `;
    document.body.appendChild(form);
    form.submit();
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="reset_settings">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
