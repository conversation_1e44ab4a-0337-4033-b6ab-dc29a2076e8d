/* Enhanced Admin Panel Styles */

:root {
    /* Primary Color Scheme */
    --primary-color: #007BFF;
    --primary-hover: #0056b3;
    --primary-light: rgba(0, 123, 255, 0.1);
    
    /* Secondary Colors */
    --secondary-color: #17A2B8;
    --secondary-hover: #138496;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Background Colors */
    --white: #FFFFFF;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --body-bg: #f4f7fc;
    --card-bg: #ffffff;
    
    /* Text Colors */
    --text-dark: #212529;
    --text-muted: #6C757D;
    --text-light: #ffffff;
    
    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    
    /* Spacing and Layout */
    --border-radius: 8px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: 0.3s ease-in-out;
    
    /* Spacing System */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--body-bg);
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-dark);
    margin: 0;
    padding: 0;
}

/* Enhanced Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    min-height: 100vh;
    position: relative;
}

/* Enhanced Sidebar */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: var(--sidebar-bg);
    color: var(--text-light);
    transition: all var(--transition);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    min-width: var(--sidebar-collapsed-width);
    max-width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    background: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    position: relative;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="8" fill="white" opacity="0.1"/><circle cx="80" cy="30" r="12" fill="white" opacity="0.1"/><circle cx="40" cy="70" r="10" fill="white" opacity="0.1"/></svg>');
    opacity: 0.3;
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 700;
    font-size: 1.3rem;
    position: relative;
    z-index: 2;
}

.sidebar.collapsed .sidebar-header h4 {
    display: none;
}

/* Sidebar Navigation */
.sidebar ul.components {
    padding: var(--spacing-lg) 0;
    margin: 0;
    list-style: none;
}

.sidebar ul li {
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.sidebar ul li a {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.95rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition);
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.sidebar ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: width var(--transition);
    z-index: 1;
}

.sidebar ul li a:hover::before,
.sidebar ul li.active > a::before {
    width: 100%;
}

.sidebar ul li a:hover {
    color: var(--white);
    border-left-color: var(--secondary-color);
    transform: translateX(5px);
}

.sidebar ul li.active > a {
    color: var(--white);
    background: rgba(255, 255, 255, 0.15);
    border-left-color: var(--secondary-color);
    box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.sidebar ul li a i {
    margin-right: var(--spacing-md);
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.sidebar ul li a .badge {
    margin-left: auto;
    position: relative;
    z-index: 2;
}

.sidebar.collapsed ul li a {
    padding: var(--spacing-md);
    text-align: center;
    justify-content: center;
}

.sidebar.collapsed ul li a span:not(.badge) {
    display: none;
}

.sidebar.collapsed ul li a i {
    margin-right: 0;
}

/* Dropdown Menu */
.sidebar ul li .collapse {
    background: rgba(0, 0, 0, 0.2);
}

.sidebar ul li .collapse li a {
    padding-left: var(--spacing-xl);
    font-size: 0.85rem;
    border-left: none;
}

.sidebar ul li .collapse li a::before {
    content: '';
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transition: all var(--transition);
}

.sidebar ul li .collapse li.active a::before,
.sidebar ul li .collapse li a:hover::before {
    background: var(--secondary-color);
    transform: translateY(-50%) scale(1.5);
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .user-info {
    display: flex;
    align-items: center;
}

.sidebar-footer .avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-sm);
}

.sidebar.collapsed .sidebar-footer .user-info > div:last-child {
    display: none;
}

/* Content Area */
.content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed + .content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Navigation */
.content .navbar {
    background: var(--white) !important;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--light-color);
    padding: var(--spacing-md) var(--spacing-lg);
    position: sticky;
    top: 0;
    z-index: 999;
}

.content .navbar .btn {
    border-radius: var(--border-radius);
    transition: all var(--transition);
}

.content .navbar .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 0.85rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-muted);
    font-weight: bold;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-hover);
}

/* Main Content */
.container-fluid {
    flex: 1;
    padding: var(--spacing-lg) !important;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition);
    background: var(--card-bg);
}

.card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.card-header {
    background: var(--light-color);
    border-bottom: 1px solid var(--light-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    padding: var(--spacing-lg);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: var(--spacing-sm);
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
