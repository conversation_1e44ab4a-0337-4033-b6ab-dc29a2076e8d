<?php
require_once '../includes/init.php';
require_once '../models/Video.php';
require_once '../models/User.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'Video Management';
$videoModel = new Video();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!Security::validateCSRFToken($_POST['csrf_token'])) {
        $_SESSION['flash_message'] = 'Invalid security token.';
        $_SESSION['flash_type'] = 'danger';
        header('Location: videos.php');
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create' || $action === 'update') {
        $data = [
            'title' => trim($_POST['title']),
            'description' => trim($_POST['description']),
            'video_url' => trim($_POST['video_url']),
            'category' => trim($_POST['category']),
            'tags' => trim($_POST['tags']),
            'duration' => !empty($_POST['duration']) ? intval($_POST['duration']) : null,
            'created_by' => $_SESSION['admin_id']
        ];
        
        // Validate required fields
        if (empty($data['title']) || empty($data['video_url'])) {
            $_SESSION['flash_message'] = 'Title and video URL are required.';
            $_SESSION['flash_type'] = 'danger';
        } else if (!$videoModel->validateVideoUrl($data['video_url'])) {
            $_SESSION['flash_message'] = 'Invalid video URL format.';
            $_SESSION['flash_type'] = 'danger';
        } else {
            // Auto-generate thumbnail for YouTube videos
            $youtubeId = $videoModel->extractYouTubeId($data['video_url']);
            if ($youtubeId) {
                $data['thumbnail'] = $videoModel->getYouTubeThumbnail($youtubeId);
            }
            
            if ($action === 'create') {
                if ($videoModel->createVideo($data)) {
                    $_SESSION['flash_message'] = 'Video created successfully.';
                    $_SESSION['flash_type'] = 'success';
                } else {
                    $_SESSION['flash_message'] = 'Failed to create video.';
                    $_SESSION['flash_type'] = 'danger';
                }
            } else {
                $videoId = intval($_POST['video_id']);
                if ($videoModel->updateVideo($videoId, $data)) {
                    $_SESSION['flash_message'] = 'Video updated successfully.';
                    $_SESSION['flash_type'] = 'success';
                } else {
                    $_SESSION['flash_message'] = 'Failed to update video.';
                    $_SESSION['flash_type'] = 'danger';
                }
            }
        }
    } else if ($action === 'delete') {
        $videoId = intval($_POST['video_id']);
        if ($videoModel->deleteVideo($videoId)) {
            $_SESSION['flash_message'] = 'Video deleted successfully.';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'Failed to delete video.';
            $_SESSION['flash_type'] = 'danger';
        }
    }
    
    header('Location: videos.php');
    exit;
}

// Get videos and categories
$videos = $videoModel->getAllVideos();
$categories = $videoModel->getCategories();
$stats = $videoModel->getVideoStats();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-video me-2"></i>Video Management</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#videoModal">
                    <i class="fas fa-plus me-2"></i>Add New Video
                </button>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_videos']; ?></h4>
                                    <p class="mb-0">Total Videos</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-video fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_categories']; ?></h4>
                                    <p class="mb-0">Categories</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-tags fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo round($stats['avg_duration'] / 60, 1); ?> min</h4>
                                    <p class="mb-0">Avg Duration</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Videos Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Videos</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($videos)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-video fa-3x text-muted mb-3"></i>
                            <h5>No videos found</h5>
                            <p class="text-muted">Start by adding your first video.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Thumbnail</th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Duration</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($videos as $video): ?>
                                    <tr>
                                        <td>
                                            <?php if ($video['thumbnail']): ?>
                                                <img src="<?php echo htmlspecialchars($video['thumbnail']); ?>" 
                                                     alt="Thumbnail" class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 80px; height: 60px;">
                                                    <i class="fas fa-video text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($video['title']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($video['description'], 0, 100)); ?>...</small>
                                        </td>
                                        <td>
                                            <?php if ($video['category']): ?>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($video['category']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($video['duration']): ?>
                                                <?php echo gmdate("H:i:s", $video['duration']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($video['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editVideo(<?php echo $video['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteVideo(<?php echo $video['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalTitle">Add New Video</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="videoForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="create">
                    <input type="hidden" name="video_id" value="">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="video_url" class="form-label">Video URL *</label>
                                <input type="url" class="form-control" id="video_url" name="video_url" required>
                                <div class="form-text">Supports YouTube, Vimeo, or direct video file URLs</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <input type="text" class="form-control" id="category" name="category" list="categoryList">
                                <datalist id="categoryList">
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags" placeholder="tag1, tag2, tag3">
                            </div>
                            
                            <div class="mb-3">
                                <label for="duration" class="form-label">Duration (seconds)</label>
                                <input type="number" class="form-control" id="duration" name="duration" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Video</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Video management JavaScript
function editVideo(id) {
    // Fetch video data and populate modal
    fetch(`../api/video.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const video = data.video;
                document.getElementById('videoModalTitle').textContent = 'Edit Video';
                document.querySelector('input[name="action"]').value = 'update';
                document.querySelector('input[name="video_id"]').value = video.id;
                document.getElementById('title').value = video.title;
                document.getElementById('description').value = video.description || '';
                document.getElementById('video_url').value = video.video_url || '';
                document.getElementById('category').value = video.category || '';
                document.getElementById('tags').value = video.tags || '';
                document.getElementById('duration').value = video.duration || '';
                
                new bootstrap.Modal(document.getElementById('videoModal')).show();
            }
        });
}

function deleteVideo(id) {
    if (confirm('Are you sure you want to delete this video?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="video_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset modal when closed
document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('videoForm').reset();
    document.getElementById('videoModalTitle').textContent = 'Add New Video';
    document.querySelector('input[name="action"]').value = 'create';
    document.querySelector('input[name="video_id"]').value = '';
});
</script>

<?php include 'includes/footer.php'; ?>
