<?php
require_once 'includes/init.php';

// Database connection
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=doctor_portfolio;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );

    // New password
    $newPassword = 'admin123';
    $newHash = password_hash($newPassword, PASSWORD_BCRYPT);
    
    // Update the admin password
    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
    $stmt->execute([$newHash]);
    
    echo "Admin password has been updated successfully!\n";
    echo "New password: $newPassword\n";
    echo "New hash: $newHash\n";
    
} catch (PDOException $e) {
    die("Error: " . $e->getMessage() . "\n");
}
