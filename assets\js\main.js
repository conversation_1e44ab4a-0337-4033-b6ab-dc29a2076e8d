/**
 * Main JavaScript file for Doctor Portfolio Website
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        if (!alert.classList.contains('alert-permanent')) {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        }
    });
    
    // Form validation helpers
    const forms = document.querySelectorAll('form[data-validate="true"]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                e.stopPropagation();
            }
            this.classList.add('was-validated');
        });
    });
    
    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatPhoneNumber(this);
        });
        
        input.addEventListener('blur', function() {
            validatePhoneNumber(this);
        });
    });
    
    // Date input restrictions
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Set minimum date to today
        if (!input.hasAttribute('min')) {
            input.setAttribute('min', new Date().toISOString().split('T')[0]);
        }
        
        input.addEventListener('change', function() {
            validateDateInput(this);
        });
    });
    
    // Loading states for forms
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        button.closest('form').addEventListener('submit', function() {
            showLoadingState(button);
        });
    });
    
    // Initialize AOS (Animate On Scroll)
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100,
            delay: 100
        });
    }

    // Initialize custom animations
    initializeAnimations();

    // Back to top button
    createBackToTopButton();

    // Initialize scrollspy for navigation
    initializeScrollSpy();

    // Initialize contact form enhancements
    initializeContactForm();

    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Initialize accessibility features
    initializeAccessibilityFeatures();

    // Initialize image lazy loading
    initializeLazyLoading();

    // Initialize theme system
    initializeThemeSystem();

    // Initialize enhanced UI features
    enhanceFormValidation();
    initializeSmoothScroll();
});

/**
 * Validate form fields
 */
function validateForm(form) {
    let isValid = true;
    
    // Check required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Validate email fields
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });
    
    // Validate phone fields
    const phoneFields = form.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
        if (field.value && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        field.parentNode.appendChild(feedback);
    }
    feedback.textContent = message;
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

/**
 * Validate email address
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate phone number (Bangladesh format)
 */
function isValidPhone(phone) {
    const phoneRegex = /^(\+880|880|0)?1[3-9]\d{8}$/;
    const cleanPhone = phone.replace(/[^0-9+]/g, '');
    return phoneRegex.test(cleanPhone);
}

/**
 * Format phone number as user types
 */
function formatPhoneNumber(input) {
    let value = input.value.replace(/[^0-9+]/g, '');
    
    if (value.startsWith('+880')) {
        value = value.replace(/(\+880)(\d{4})(\d{6})/, '$1-$2-$3');
    } else if (value.startsWith('880')) {
        value = value.replace(/(880)(\d{4})(\d{6})/, '+$1-$2-$3');
    } else if (value.startsWith('01')) {
        value = value.replace(/(01)(\d{4})(\d{5})/, '+880-1$2-$3');
    }
    
    input.value = value;
}

/**
 * Validate phone number on blur
 */
function validatePhoneNumber(input) {
    if (input.value && !isValidPhone(input.value)) {
        showFieldError(input, 'Please enter a valid Bangladesh phone number');
    } else {
        clearFieldError(input);
    }
}

/**
 * Validate date input
 */
function validateDateInput(input) {
    const selectedDate = new Date(input.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        showFieldError(input, 'Please select a future date');
        input.value = '';
    } else {
        clearFieldError(input);
    }
}

/**
 * Show loading state for button
 */
function showLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';
    button.disabled = true;
    
    // Reset after 10 seconds (fallback)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 10000);
}

/**
 * Initialize animations
 */
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements with animation classes
    document.querySelectorAll('.slide-up, .fade-in-delay').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Create back to top button
 */
function createBackToTopButton() {
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '<i class="bi bi-arrow-up"></i>';
    backToTop.className = 'btn btn-primary-custom position-fixed';
    backToTop.style.cssText = `
        bottom: 80px;
        left: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: none;
        z-index: 999;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;
    
    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
    
    document.body.appendChild(backToTop);
    
    // Show/hide based on scroll position
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.display = 'block';
        } else {
            backToTop.style.display = 'none';
        }
    });
}

/**
 * Utility function to show toast notifications
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * Create toast container if it doesn't exist
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

/**
 * Debounce function for performance optimization
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Format currency for display
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-BD', {
        style: 'currency',
        currency: 'BDT',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Failed to copy to clipboard', 'danger');
    });
}

/**
 * Initialize ScrollSpy for navigation highlighting
 */
function initializeScrollSpy() {
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const sections = document.querySelectorAll('section[id]');

    if (sections.length === 0) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const navLink = document.querySelector(`.navbar-nav .nav-link[href="#${entry.target.id}"]`);
            if (entry.isIntersecting && navLink) {
                navLinks.forEach(link => link.classList.remove('active'));
                navLink.classList.add('active');
            }
        });
    }, {
        threshold: 0.3,
        rootMargin: '-20% 0px -20% 0px'
    });

    sections.forEach(section => observer.observe(section));
}

/**
 * Enhanced animation utilities
 */
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);

    const timer = setInterval(() => {
        start += increment;
        element.textContent = Math.floor(start);

        if (start >= target) {
            element.textContent = target;
            clearInterval(timer);
        }
    }, 16);
}

/**
 * Parallax effect for hero section
 */
function initializeParallax() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    window.addEventListener('scroll', debounce(() => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        heroSection.style.transform = `translateY(${rate}px)`;
    }, 10));
}

/**
 * Initialize typing animation effect
 */
function initializeTypingAnimation(element, texts, speed = 100) {
    if (!element || !texts.length) return;

    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;

    function type() {
        const currentText = texts[textIndex];

        if (isDeleting) {
            element.textContent = currentText.substring(0, charIndex - 1);
            charIndex--;
        } else {
            element.textContent = currentText.substring(0, charIndex + 1);
            charIndex++;
        }

        let typeSpeed = speed;

        if (isDeleting) {
            typeSpeed /= 2;
        }

        if (!isDeleting && charIndex === currentText.length) {
            typeSpeed = 2000; // Pause at end
            isDeleting = true;
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            textIndex = (textIndex + 1) % texts.length;
            typeSpeed = 500; // Pause before next text
        }

        setTimeout(type, typeSpeed);
    }

    type();
}

/**
 * Scroll to map section smoothly
 */
function scrollToMap() {
    const mapSection = document.getElementById('map');
    if (mapSection) {
        mapSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * Enhanced contact form functionality
 */
function initializeContactForm() {
    const contactForm = document.getElementById('contactForm');
    if (!contactForm) return;

    // Add character counter for message field
    const messageField = document.getElementById('message');
    if (messageField) {
        const maxLength = 500;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.innerHTML = `<span id="charCount">0</span>/${maxLength} characters`;
        messageField.parentNode.appendChild(counter);

        messageField.addEventListener('input', function() {
            const count = this.value.length;
            document.getElementById('charCount').textContent = count;

            if (count > maxLength * 0.9) {
                counter.classList.add('text-warning');
            } else {
                counter.classList.remove('text-warning');
            }

            if (count > maxLength) {
                counter.classList.add('text-danger');
                counter.classList.remove('text-warning');
            } else {
                counter.classList.remove('text-danger');
            }
        });
    }

    // Add form submission enhancement
    contactForm.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Sending...';
            submitBtn.disabled = true;

            // Re-enable after 10 seconds (fallback)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        }
    });
}

/**
 * Initialize performance optimizations
 */
function initializePerformanceOptimizations() {
    // Preload critical resources
    preloadCriticalResources();

    // Add loading states to all buttons
    addLoadingStates();

    // Implement service worker for caching (if supported)
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').catch(() => {
            // Service worker registration failed, continue without it
        });
    }
}

/**
 * Preload critical resources
 */
function preloadCriticalResources() {
    const criticalResources = [
        'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        'assets/css/style.css'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        link.href = resource;
        document.head.appendChild(link);
    });
}

/**
 * Add loading states to buttons
 */
function addLoadingStates() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
                submitBtn.disabled = true;

                // Store original text for potential restoration
                submitBtn.dataset.originalText = originalText;
            }
        });
    });
}

/**
 * Initialize accessibility features
 */
function initializeAccessibilityFeatures() {
    // Add ARIA labels to interactive elements
    addAriaLabels();

    // Implement keyboard navigation
    implementKeyboardNavigation();

    // Add focus management
    addFocusManagement();

    // Add skip links
    addSkipLinks();
}

/**
 * Add ARIA labels to elements that need them
 */
function addAriaLabels() {
    // Add ARIA labels to buttons without text
    document.querySelectorAll('button:not([aria-label])').forEach(button => {
        const icon = button.querySelector('i');
        if (icon && !button.textContent.trim()) {
            const iconClass = icon.className;
            let label = 'Button';

            if (iconClass.includes('search')) label = 'Search';
            else if (iconClass.includes('menu')) label = 'Menu';
            else if (iconClass.includes('close')) label = 'Close';
            else if (iconClass.includes('phone')) label = 'Call';
            else if (iconClass.includes('envelope')) label = 'Email';
            else if (iconClass.includes('whatsapp')) label = 'WhatsApp';

            button.setAttribute('aria-label', label);
        }
    });

    // Add ARIA labels to form inputs without labels
    document.querySelectorAll('input:not([aria-label]):not([id])').forEach(input => {
        const placeholder = input.getAttribute('placeholder');
        if (placeholder) {
            input.setAttribute('aria-label', placeholder);
        }
    });
}

/**
 * Implement keyboard navigation
 */
function implementKeyboardNavigation() {
    // Add keyboard support for custom interactive elements
    document.querySelectorAll('.time-slot, .nav-link').forEach(element => {
        if (!element.hasAttribute('tabindex')) {
            element.setAttribute('tabindex', '0');
        }

        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    });
}

/**
 * Add focus management
 */
function addFocusManagement() {
    // Focus management for modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            const firstFocusable = this.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        });
    });

    // Focus visible outline for keyboard users
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

/**
 * Add skip links for screen readers
 */
function addSkipLinks() {
    if (!document.getElementById('skip-links')) {
        const skipLinks = document.createElement('div');
        skipLinks.id = 'skip-links';
        skipLinks.innerHTML = `
            <a href="#main-content" class="skip-link">Skip to main content</a>
            <a href="#navigation" class="skip-link">Skip to navigation</a>
        `;
        document.body.insertBefore(skipLinks, document.body.firstChild);
    }
}

/**
 * High contrast mode toggle has been removed as per user request
 */

/**
 * Initialize lazy loading for images
 */
function initializeLazyLoading() {
    // Add lazy loading to all images
    document.querySelectorAll('img:not([loading])').forEach(img => {
        img.setAttribute('loading', 'lazy');
    });

    // Add intersection observer for fade-in effect
    if ('IntersectionObserver' in window) {
        const fadeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    fadeObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.lazy-fade').forEach(el => {
            fadeObserver.observe(el);
        });
    }
}

/**
 * Initialize Theme System
 */
function initializeThemeSystem() {
    // Get saved theme or default to light
    const savedTheme = localStorage.getItem('theme') || 'light';

    // Apply saved theme
    setTheme(savedTheme);

    // Add event listeners for theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
        themeToggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleTheme();
            }
        });
    }

    // Listen for system theme changes
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', handleSystemThemeChange);
    }
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

/**
 * Set theme and save preference
 */
function setTheme(theme) {
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);

    // Save to localStorage
    localStorage.setItem('theme', theme);

    // Update theme toggle button state
    updateThemeToggleState(theme);

    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));

    // Add smooth transition class
    document.body.classList.add('theme-transitioning');
    setTimeout(() => {
        document.body.classList.remove('theme-transitioning');
    }, 300);
}

/**
 * Update theme toggle button visual state
 */
function updateThemeToggleState(theme) {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.setAttribute('aria-pressed', theme === 'dark');
        themeToggle.title = theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode';
    }
}

/**
 * Handle system theme preference changes
 */
function handleSystemThemeChange(e) {
    // Only auto-switch if user hasn't manually set a preference
    if (!localStorage.getItem('theme')) {
        const systemTheme = e.matches ? 'dark' : 'light';
        setTheme(systemTheme);
    }
}

/**
 * Get current theme
 */
function getCurrentTheme() {
    return document.documentElement.getAttribute('data-theme') || 'light';
}

/**
 * Enhanced theme-aware animations
 */
function initializeThemeAwareAnimations() {
    // Add theme-specific animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const theme = getCurrentTheme();
                entry.target.classList.add('animate-in');
                entry.target.classList.add(`animate-${theme}`);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.theme-animate').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Theme-aware gradient backgrounds
 */
function updateGradientBackgrounds() {
    const theme = getCurrentTheme();
    const gradientElements = document.querySelectorAll('.gradient-bg');

    gradientElements.forEach(element => {
        if (theme === 'dark') {
            element.style.background = 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)';
        } else {
            element.style.background = 'linear-gradient(135deg, #007BFF 0%, #17A2B8 100%)';
        }
    });
}

// Listen for theme changes to update gradients
window.addEventListener('themeChanged', updateGradientBackgrounds);

/**
 * Video functionality
 */
function playVideo(videoUrl, title) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const videoFrame = document.getElementById('videoFrame');
    const modalTitle = document.getElementById('videoModalLabel');

    // Set modal title
    modalTitle.textContent = title;

    // Convert video URL to embeddable format
    let embedUrl = '';

    // YouTube URL conversion
    const youtubeMatch = videoUrl.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
    if (youtubeMatch) {
        embedUrl = `https://www.youtube.com/embed/${youtubeMatch[1]}?autoplay=1&rel=0`;
    }

    // Vimeo URL conversion
    const vimeoMatch = videoUrl.match(/vimeo\.com\/(\d+)/);
    if (vimeoMatch) {
        embedUrl = `https://player.vimeo.com/video/${vimeoMatch[1]}?autoplay=1`;
    }

    // Direct video file
    if (videoUrl.match(/\.(mp4|avi|mov|wmv|webm)$/i)) {
        embedUrl = videoUrl;
    }

    // Set video source
    videoFrame.src = embedUrl;

    // Show modal
    modal.show();

    // Clear video when modal is closed
    document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
        videoFrame.src = '';
    }, { once: true });
}

/**
 * Initialize video lazy loading
 */
function initializeVideoLazyLoading() {
    const videoThumbnails = document.querySelectorAll('.video-thumb[data-src]');

    if ('IntersectionObserver' in window) {
        const videoObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    videoObserver.unobserve(img);
                }
            });
        }, { threshold: 0.1 });

        videoThumbnails.forEach(img => videoObserver.observe(img));
    }
}

/**
 * Video card animations
 */
function initializeVideoAnimations() {
    const videoCards = document.querySelectorAll('.video-card');

    videoCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Initialize video features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeVideoLazyLoading();
    initializeVideoAnimations();
    initializePageTransitions();
    initializeMicroInteractions();
    initializeStaggeredAnimations();
});

/**
 * Initialize page transition effects
 */
function initializePageTransitions() {
    // Add page transition class to main content
    const mainContent = document.querySelector('main') || document.body;
    mainContent.classList.add('page-transition');

    // Trigger transition after a short delay
    setTimeout(() => {
        mainContent.classList.add('loaded');
    }, 100);

    // Handle link transitions
    document.querySelectorAll('a[href^="/"], a[href^="./"], a[href^="../"]').forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.target !== '_blank') {
                e.preventDefault();
                const href = this.href;

                // Add exit animation
                mainContent.classList.remove('loaded');

                // Navigate after animation
                setTimeout(() => {
                    window.location.href = href;
                }, 300);
            }
        });
    });
}

/**
 * Initialize micro-interactions
 */
function initializeMicroInteractions() {
    // Add bounce effect to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.classList.add('micro-bounce');
    });

    // Add shake effect to form validation errors
    document.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.add('micro-shake');
    });

    // Enhanced hover effects for cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Ripple effect for buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Initialize staggered animations
 */
function initializeStaggeredAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('stagger-animation');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    // Apply to navigation items, card grids, etc.
    document.querySelectorAll('.navbar-nav, .row .col-md-4, .row .col-lg-4').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Enhanced form validation with animations
 */
function enhanceFormValidation() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const invalidFields = this.querySelectorAll(':invalid');

            if (invalidFields.length > 0) {
                e.preventDefault();

                // Animate invalid fields
                invalidFields.forEach((field, index) => {
                    setTimeout(() => {
                        field.classList.add('micro-shake');
                        field.focus();

                        setTimeout(() => {
                            field.classList.remove('micro-shake');
                        }, 500);
                    }, index * 100);
                });
            }
        });
    });
}

/**
 * Smooth scroll with easing
 */
function initializeSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));

            if (target) {
                const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - 80;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Add CSS for ripple animation
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
