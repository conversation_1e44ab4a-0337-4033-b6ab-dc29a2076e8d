<?php
require_once 'includes/init.php';
require_once 'models/DoctorProfile.php';
require_once 'models/Video.php';

$pageTitle = 'Home';
$doctorProfileModel = new DoctorProfile();
$videoModel = new Video();
$doctorProfile = $doctorProfileModel->getActiveProfile();

if (!$doctorProfile) {
    die('Doctor profile not found. Please run the installation script first.');
}

// Parse work schedule
$workSchedule = json_decode($doctorProfile['work_schedule'], true);

// Get featured videos
$featuredVideos = $videoModel->getFeaturedVideos(3);
?>

<?php include 'includes/header.php'; ?>

<!-- Hero Section -->
<section class="hero-section" id="home">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6" data-aos="fade-right" data-aos-duration="1000">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        Welcome to
                        <span class="gradient-text">Dr. <?php echo explode(' ', $doctorProfile['full_name'])[1] ?? $doctorProfile['full_name']; ?></span>
                    </h1>
                    <p class="lead mb-4">
                        <?php echo htmlspecialchars($doctorProfile['specialization']); ?> with
                        <strong><?php echo $doctorProfile['years_experience']; ?>+ years</strong>
                        of experience providing comprehensive medical care and dedication to your health and well-being.
                    </p>
                    <div class="hero-buttons">
                        <a href="appointment.php" class="btn btn-light btn-lg shadow-lg">
                            <i class="fas fa-calendar-check me-2"></i>Book Appointment
                        </a>
                        <a href="contact.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>Contact Us
                        </a>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row mt-5 text-center stagger-animation">
                        <div class="col-4">
                            <div class="stat-item micro-bounce">
                                <h3 class="fw-bold mb-1 gradient-text"><?php echo $doctorProfile['years_experience']; ?>+</h3>
                                <small class="text-light opacity-75">Years Experience</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item micro-bounce">
                                <h3 class="fw-bold mb-1 gradient-text">1000+</h3>
                                <small class="text-light opacity-75">Happy Patients</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item micro-bounce">
                                <h3 class="fw-bold mb-1 gradient-text">24/7</h3>
                                <small class="text-light opacity-75">Emergency Care</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
                <div class="hero-image">
                    <?php if ($doctorProfile['profile_photo']): ?>
                        <div class="hero-doctor-image">
                            <img src="<?php echo htmlspecialchars($doctorProfile['profile_photo']); ?>"
                                 alt="<?php echo htmlspecialchars($doctorProfile['full_name']); ?>"
                                 class="img-fluid rounded-circle shadow-lg"
                                 style="width: 400px; height: 400px; object-fit: cover; border: 8px solid rgba(255,255,255,0.2);">
                        </div>
                    <?php else: ?>
                        <i class="bi bi-heart-pulse"></i>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Doctor Profile Section -->
<section class="py-5" id="about">
    <div class="container">
        <div class="profile-card" data-aos="fade-up" data-aos-duration="800">
            <div class="row g-0">
                <div class="col-lg-4 text-center p-4" data-aos="zoom-in" data-aos-delay="200">
                    <?php if ($doctorProfile['profile_photo']): ?>
                        <img src="<?php echo htmlspecialchars($doctorProfile['profile_photo']); ?>"
                             alt="<?php echo htmlspecialchars($doctorProfile['full_name']); ?>"
                             class="profile-image">
                    <?php else: ?>
                        <div class="profile-image bg-primary-custom d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-fill text-white" style="font-size: 4rem;"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-lg-8 p-4" data-aos="fade-left" data-aos-delay="400">
                    <div class="profile-info">
                        <h2><?php echo htmlspecialchars($doctorProfile['full_name']); ?></h2>
                        <p class="text-muted"><?php echo htmlspecialchars($doctorProfile['medical_degrees']); ?></p>
                        <h4 class="text-primary-custom mb-3"><?php echo htmlspecialchars($doctorProfile['specialization']); ?></h4>

                        <div class="row mb-4" data-aos="fade-up" data-aos-delay="600">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                                    <div class="icon-wrapper me-3">
                                        <i class="fas fa-award text-primary-custom fs-4"></i>
                                    </div>
                                    <div>
                                        <strong class="d-block"><?php echo $doctorProfile['years_experience']; ?> Years</strong>
                                        <small class="text-muted">of Experience</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                                    <div class="icon-wrapper me-3">
                                        <i class="fas fa-phone text-primary-custom fs-4"></i>
                                    </div>
                                    <div>
                                        <strong class="d-block"><?php echo formatPhone($doctorProfile['phone']); ?></strong>
                                        <small class="text-muted">Phone Number</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                                    <div class="icon-wrapper me-3">
                                        <i class="fas fa-envelope text-primary-custom fs-4"></i>
                                    </div>
                                    <div>
                                        <strong class="d-block"><?php echo htmlspecialchars($doctorProfile['email']); ?></strong>
                                        <small class="text-muted">Email Address</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                                    <div class="icon-wrapper me-3">
                                        <i class="fas fa-dollar-sign text-primary-custom fs-4"></i>
                                    </div>
                                    <div>
                                        <strong class="d-block">৳<?php echo number_format($doctorProfile['consultation_fee']); ?></strong>
                                        <small class="text-muted">Consultation Fee</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="800">
                            <a href="appointment.php" class="btn btn-primary-custom btn-lg">
                                <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                            </a>
                            <a href="contact.php" class="btn btn-outline-primary-custom btn-lg">
                                <i class="fas fa-comments me-2"></i>Send Message
                            </a>
                            <?php if ($doctorProfile['whatsapp']): ?>
                            <a href="<?php echo generateWhatsAppURL($doctorProfile['whatsapp'], 'Hello Dr. ' . $doctorProfile['full_name'] . ', I would like to schedule an appointment.'); ?>"
                               class="btn btn-success btn-lg" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Work Schedule Section -->
<section class="py-5 bg-light" id="schedule">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5" data-aos="fade-up">
                    <h3 class="display-6 fw-bold text-primary-custom">Work Schedule</h3>
                    <p class="lead text-muted">Check our available consultation hours</p>
                </div>
                <div class="schedule-table" data-aos="fade-up" data-aos-delay="200">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th>Day</th>
                                <th>Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workSchedule as $day => $schedule): ?>
                            <tr>
                                <td><strong><?php echo getDayName($day); ?></strong></td>
                                <td>
                                    <?php if ($schedule === 'closed' || (is_array($schedule) && isset($schedule['closed']))): ?>
                                        <span class="text-muted">Closed</span>
                                    <?php else: ?>
                                        <?php echo formatTime($schedule['start']) . ' - ' . formatTime($schedule['end']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($schedule === 'closed' || (is_array($schedule) && isset($schedule['closed']))): ?>
                                        <span class="badge bg-danger">Closed</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Open</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Detailed Information Tabs -->
<section class="py-5" id="details">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h3 class="display-6 fw-bold text-primary-custom">Professional Details</h3>
            <p class="lead text-muted">Learn more about our expertise and achievements</p>
        </div>
        <div class="row">
            <div class="col-lg-10 mx-auto" data-aos="fade-up" data-aos-delay="200">
                <ul class="nav nav-tabs justify-content-center" id="doctorTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="biography-tab" data-bs-toggle="tab" data-bs-target="#biography" type="button" role="tab">
                            <i class="fas fa-user-md me-2"></i>Biography
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="certifications-tab" data-bs-toggle="tab" data-bs-target="#certifications" type="button" role="tab">
                            <i class="fas fa-certificate me-2"></i>Certifications
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="awards-tab" data-bs-toggle="tab" data-bs-target="#awards" type="button" role="tab">
                            <i class="fas fa-trophy me-2"></i>Awards
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="doctorTabsContent">
                    <!-- Biography Tab -->
                    <div class="tab-pane fade show active" id="biography" role="tabpanel">
                        <h4 class="mb-3">About Dr. <?php echo htmlspecialchars($doctorProfile['full_name']); ?></h4>
                        <p class="lead"><?php echo nl2br(htmlspecialchars($doctorProfile['biography'])); ?></p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5>Clinic Address</h5>
                                <p><?php echo nl2br(htmlspecialchars($doctorProfile['clinic_address'])); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h5>Contact Information</h5>
                                <p>
                                    <i class="bi bi-telephone me-2"></i><?php echo formatPhone($doctorProfile['phone']); ?><br>
                                    <i class="bi bi-envelope me-2"></i><?php echo htmlspecialchars($doctorProfile['email']); ?>
                                    <?php if ($doctorProfile['whatsapp']): ?>
                                    <br><i class="bi bi-whatsapp me-2"></i><?php echo formatPhone($doctorProfile['whatsapp']); ?>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Certifications Tab -->
                    <div class="tab-pane fade" id="certifications" role="tabpanel">
                        <h4 class="mb-3">Professional Certifications</h4>
                        <?php 
                        $certifications = $doctorProfileModel->getCertifications($doctorProfile['id']);
                        if ($certifications): 
                        ?>
                        <div class="row">
                            <?php foreach ($certifications as $cert): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($cert['title']); ?></h5>
                                        <p class="card-text">
                                            <strong>Issued by:</strong> <?php echo htmlspecialchars($cert['issuing_organization']); ?><br>
                                            <strong>Date:</strong> <?php echo formatDate($cert['issue_date']); ?>
                                            <?php if ($cert['expiry_date']): ?>
                                            <br><strong>Expires:</strong> <?php echo formatDate($cert['expiry_date']); ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <p class="text-muted">No certifications available.</p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Awards Tab -->
                    <div class="tab-pane fade" id="awards" role="tabpanel">
                        <h4 class="mb-3">Awards & Recognition</h4>
                        <?php 
                        $awards = $doctorProfileModel->getAwards($doctorProfile['id']);
                        if ($awards): 
                        ?>
                        <div class="row">
                            <?php foreach ($awards as $award): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($award['title']); ?></h5>
                                        <p class="card-text">
                                            <strong>Awarded by:</strong> <?php echo htmlspecialchars($award['awarding_organization']); ?><br>
                                            <strong>Date:</strong> <?php echo formatDate($award['award_date']); ?>
                                            <?php if ($award['description']): ?>
                                            <br><br><?php echo htmlspecialchars($award['description']); ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <p class="text-muted">No awards available.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Video Section -->
<?php if (!empty($featuredVideos)): ?>
<section class="py-5 bg-light" id="videos">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h3 class="display-6 fw-bold text-primary-custom">Health Education Videos</h3>
            <p class="lead text-muted">Watch our educational videos to learn more about health and wellness</p>
        </div>

        <div class="row">
            <?php foreach ($featuredVideos as $index => $video): ?>
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 200; ?>">
                <div class="video-card card h-100 border-0 shadow-sm">
                    <div class="video-thumbnail position-relative">
                        <?php
                        $youtubeId = $videoModel->extractYouTubeId($video['video_url']);
                        $thumbnailUrl = $video['thumbnail'] ?: ($youtubeId ? $videoModel->getYouTubeThumbnail($youtubeId) : 'assets/images/video-placeholder.jpg');
                        ?>
                        <img src="<?php echo htmlspecialchars($thumbnailUrl); ?>"
                             alt="<?php echo htmlspecialchars($video['title']); ?>"
                             class="card-img-top video-thumb"
                             style="height: 200px; object-fit: cover;">

                        <div class="video-overlay">
                            <?php
                            $videoSource = $video['video_url'] ?: ($video['video_file'] ? BASE_URL . $video['video_file'] : '');
                            ?>
                            <button class="btn btn-primary btn-lg rounded-circle video-play-btn"
                                    onclick="playVideo('<?php echo htmlspecialchars($videoSource); ?>', '<?php echo htmlspecialchars($video['title']); ?>')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>

                        <?php if ($video['duration']): ?>
                        <div class="video-duration">
                            <?php echo gmdate("i:s", $video['duration']); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($video['category']): ?>
                        <div class="video-category">
                            <span class="badge bg-primary"><?php echo htmlspecialchars($video['category']); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($video['title']); ?></h5>
                        <p class="card-text text-muted">
                            <?php echo htmlspecialchars(substr($video['description'], 0, 120)); ?>
                            <?php if (strlen($video['description']) > 120): ?>...<?php endif; ?>
                        </p>

                        <?php if ($video['tags']): ?>
                        <div class="video-tags">
                            <?php
                            $tags = explode(',', $video['tags']);
                            foreach (array_slice($tags, 0, 3) as $tag):
                            ?>
                            <span class="badge bg-light text-dark me-1"><?php echo htmlspecialchars(trim($tag)); ?></span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="card-footer bg-transparent border-0">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date('M j, Y', strtotime($video['created_at'])); ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="800">
            <a href="videos.php" class="btn btn-outline-primary-custom btn-lg">
                <i class="fas fa-video me-2"></i>View All Videos
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalLabel">Video</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <iframe id="videoFrame" src="" frameborder="0" allowfullscreen allow="autoplay"></iframe>
                </div>
                <div id="videoPlayer" style="display: none;">
                    <video id="videoElement" controls autoplay width="100%" height="100%">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Play video in modal
 */
function playVideo(videoUrl, title) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const modalTitle = document.getElementById('videoModalLabel');
    const videoFrame = document.getElementById('videoFrame');
    const videoPlayer = document.getElementById('videoPlayer');
    const videoElement = document.getElementById('videoElement');

    modalTitle.textContent = title;

    // Check if it's a YouTube video
    const youtubeMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    const vimeoMatch = videoUrl.match(/vimeo\.com\/(\d+)/);

    if (youtubeMatch) {
        // YouTube video
        const videoId = youtubeMatch[1];
        videoFrame.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
        videoFrame.style.display = 'block';
        videoPlayer.style.display = 'none';
    } else if (vimeoMatch) {
        // Vimeo video
        const videoId = vimeoMatch[1];
        videoFrame.src = `https://player.vimeo.com/video/${videoId}?autoplay=1`;
        videoFrame.style.display = 'block';
        videoPlayer.style.display = 'none';
    } else {
        // Direct video file or other URL
        videoElement.src = videoUrl;
        videoFrame.style.display = 'none';
        videoPlayer.style.display = 'block';
    }

    modal.show();
}

// Clear video when modal is closed
document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
    const videoFrame = document.getElementById('videoFrame');
    const videoElement = document.getElementById('videoElement');

    videoFrame.src = '';
    videoElement.src = '';
    videoElement.pause();
});
</script>

<?php include 'includes/footer.php'; ?>
