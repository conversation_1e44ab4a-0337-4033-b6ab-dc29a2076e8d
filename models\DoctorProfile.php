<?php
/**
 * Doctor Profile Model
 * Handles doctor profile data management
 */

class DoctorProfile {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get doctor profile by ID
     */
    public function getProfileById($id) {
        $sql = "SELECT * FROM doctor_profile WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * Get active doctor profile
     * Returns the first active doctor profile (for single doctor website)
     */
    public function getActiveProfile() {
        $sql = "SELECT * FROM doctor_profile WHERE is_active = 1 LIMIT 1";
        return $this->db->fetch($sql, []);
    }
    
    /**
     * Update doctor profile
     */
    public function updateProfile($id, $data) {
        try {
            $sql = "UPDATE doctor_profile SET 
                    full_name = ?, 
                    medical_degrees = ?, 
                    specialization = ?, 
                    years_experience = ?, 
                    clinic_address = ?, 
                    phone = ?, 
                    email = ?, 
                    whatsapp = ?, 
                    biography = ?, 
                    work_schedule = ?, 
                    consultation_fee = ? 
                    WHERE id = ?";
            
            $this->db->execute($sql, [
                $data['full_name'],
                $data['medical_degrees'],
                $data['specialization'],
                $data['years_experience'],
                $data['clinic_address'],
                $data['phone'],
                $data['email'],
                $data['whatsapp'],
                $data['biography'],
                $data['work_schedule'],
                $data['consultation_fee'],
                $id
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update profile photo
     */
    public function updateProfilePhoto($id, $photoPath) {
        try {
            $sql = "UPDATE doctor_profile SET profile_photo = ? WHERE id = ?";
            $this->db->execute($sql, [$photoPath, $id]);
            return true;
        } catch (Exception $e) {
            error_log("Profile photo update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get certifications for a doctor
     */
    public function getCertifications($doctorId) {
        $sql = "SELECT * FROM certifications WHERE doctor_id = ? ORDER BY issue_date DESC";
        return $this->db->fetchAll($sql, [$doctorId]);
    }
    
    /**
     * Add certification
     */
    public function addCertification($data) {
        try {
            $sql = "INSERT INTO certifications (doctor_id, title, issuing_organization, issue_date, expiry_date, certificate_image) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $this->db->execute($sql, [
                $data['doctor_id'],
                $data['title'],
                $data['issuing_organization'],
                $data['issue_date'],
                $data['expiry_date'] ?? null,
                $data['certificate_image'] ?? null
            ]);
            
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Add certification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update certification
     */
    public function updateCertification($id, $data) {
        try {
            $sql = "UPDATE certifications SET 
                    title = ?, 
                    issuing_organization = ?, 
                    issue_date = ?, 
                    expiry_date = ? 
                    WHERE id = ? AND doctor_id = ?";
            
            $this->db->execute($sql, [
                $data['title'],
                $data['issuing_organization'],
                $data['issue_date'],
                $data['expiry_date'] ?? null,
                $id,
                $data['doctor_id']
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Update certification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete certification
     */
    public function deleteCertification($id, $doctorId) {
        try {
            $sql = "DELETE FROM certifications WHERE id = ? AND doctor_id = ?";
            $this->db->execute($sql, [$id, $doctorId]);
            return true;
        } catch (Exception $e) {
            error_log("Delete certification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get awards for a doctor
     */
    public function getAwards($doctorId) {
        $sql = "SELECT * FROM awards WHERE doctor_id = ? ORDER BY award_date DESC";
        return $this->db->fetchAll($sql, [$doctorId]);
    }
    
    /**
     * Add award
     */
    public function addAward($data) {
        try {
            $sql = "INSERT INTO awards (doctor_id, title, awarding_organization, award_date, description, award_image) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $this->db->execute($sql, [
                $data['doctor_id'],
                $data['title'],
                $data['awarding_organization'],
                $data['award_date'],
                $data['description'] ?? null,
                $data['award_image'] ?? null
            ]);
            
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Add award error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update award
     */
    public function updateAward($id, $data) {
        try {
            $sql = "UPDATE awards SET 
                    title = ?, 
                    awarding_organization = ?, 
                    award_date = ?, 
                    description = ? 
                    WHERE id = ? AND doctor_id = ?";
            
            $this->db->execute($sql, [
                $data['title'],
                $data['awarding_organization'],
                $data['award_date'],
                $data['description'] ?? null,
                $id,
                $data['doctor_id']
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Update award error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete award
     */
    public function deleteAward($id, $doctorId) {
        try {
            $sql = "DELETE FROM awards WHERE id = ? AND doctor_id = ?";
            $this->db->execute($sql, [$id, $doctorId]);
            return true;
        } catch (Exception $e) {
            error_log("Delete award error: " . $e->getMessage());
            return false;
        }
    }
}
?>
