<?php
require_once 'includes/init.php';
require_once 'models/DoctorProfile.php';

$pageTitle = 'Contact Us';
$doctorProfileModel = new DoctorProfile();
$doctorProfile = $doctorProfileModel->getActiveProfile();

$errors = [];
$success = false;

// Process contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid form submission. Please try again.';
    } else {
        // Sanitize input
        $data = Security::sanitizeInput($_POST);
        
        // Validate required fields
        if (empty($data['name'])) {
            $errors[] = 'Name is required.';
        }
        
        if (empty($data['email'])) {
            $errors[] = 'Email address is required.';
        } elseif (!Security::validateEmail($data['email'])) {
            $errors[] = 'Please enter a valid email address.';
        }
        
        if (!empty($data['phone']) && !Security::validatePhone($data['phone'])) {
            $errors[] = 'Please enter a valid phone number.';
        }
        
        if (empty($data['message'])) {
            $errors[] = 'Message is required.';
        }
        
        // If no errors, save contact message
        if (empty($errors)) {
            try {
                $db = Database::getInstance();
                $sql = "INSERT INTO contact_messages (name, email, phone, subject, message) VALUES (?, ?, ?, ?, ?)";
                $db->execute($sql, [
                    $data['name'],
                    $data['email'],
                    $data['phone'] ?? null,
                    $data['subject'] ?? null,
                    $data['message']
                ]);
                
                $success = true;
                
                // Send notification email to admin (optional)
                $subject = "New Contact Message from " . $data['name'];
                $message = "
                <h2>New Contact Message</h2>
                <p><strong>Name:</strong> {$data['name']}</p>
                <p><strong>Email:</strong> {$data['email']}</p>
                <p><strong>Phone:</strong> " . ($data['phone'] ?? 'Not provided') . "</p>
                <p><strong>Subject:</strong> " . ($data['subject'] ?? 'No subject') . "</p>
                <p><strong>Message:</strong></p>
                <p>" . nl2br(htmlspecialchars($data['message'])) . "</p>
                ";
                
                if ($doctorProfile && $doctorProfile['email']) {
                    sendEmail($doctorProfile['email'], $subject, $message);
                }
                
                // Clear form data
                $_POST = [];
                
            } catch (Exception $e) {
                $errors[] = 'Failed to send message. Please try again.';
                error_log("Contact form error: " . $e->getMessage());
            }
        }
    }
}
?>

<?php include 'includes/header.php'; ?>

<!-- Enhanced Page Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="mb-4">
                    <i class="fas fa-comments display-1 mb-3 opacity-75"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">Get In <span class="gradient-text">Touch</span></h1>
                <p class="lead mb-4">We're here to help and answer any questions you might have</p>
                <div class="row text-center">
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-item">
                            <i class="fas fa-reply fs-2 mb-2"></i>
                            <h6>Quick Response</h6>
                            <small class="opacity-75">We reply within 24 hours</small>
                        </div>
                    </div>
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-item">
                            <i class="fas fa-user-md fs-2 mb-2"></i>
                            <h6>Professional Care</h6>
                            <small class="opacity-75">Expert medical advice</small>
                        </div>
                    </div>
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="600">
                        <div class="feature-item">
                            <i class="fas fa-heart fs-2 mb-2"></i>
                            <h6>Compassionate Service</h6>
                            <small class="opacity-75">We care about your health</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <?php if ($success): ?>
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="alert alert-success text-center">
                    <i class="bi bi-check-circle-fill fs-1 text-success mb-3 d-block"></i>
                    <h4>Message Sent Successfully!</h4>
                    <p>Thank you for contacting us. We have received your message and will get back to you as soon as possible.</p>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary me-2">Back to Home</a>
                        <a href="contact.php" class="btn btn-outline-primary">Send Another Message</a>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <div class="card shadow-lg border-0" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header bg-gradient-primary text-white border-0">
                        <h4 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Send us a Message</h4>
                        <small class="opacity-75">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</small>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" id="contactForm" data-validate="true">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="name" name="name"
                                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                               placeholder="Full Name" required>
                                        <label for="name">
                                            <i class="fas fa-user me-2"></i>Full Name *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                               placeholder="Email Address" required>
                                        <label for="email">
                                            <i class="fas fa-envelope me-2"></i>Email Address *
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                               placeholder="+880-1XXX-XXXXXX">
                                        <label for="phone">
                                            <i class="fas fa-phone me-2"></i>Phone Number (Optional)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="subject" name="subject"
                                               value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                                               placeholder="Subject">
                                        <label for="subject">
                                            <i class="fas fa-tag me-2"></i>Subject (Optional)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-floating">
                                    <textarea class="form-control" id="message" name="message"
                                              style="height: 120px" placeholder="Your Message" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                    <label for="message">
                                        <i class="fas fa-comment-dots me-2"></i>Your Message *
                                    </label>
                                </div>
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Please provide as much detail as possible to help us assist you better.
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary-custom btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info-enhanced" data-aos="fade-up" data-aos-delay="400">
                    <div class="card border-0 shadow-lg h-100">
                        <div class="card-header bg-gradient-primary text-white border-0">
                            <h4 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h4>
                            <small class="opacity-75">Multiple ways to reach us</small>
                        </div>
                        <div class="card-body p-4">
                            <?php if ($doctorProfile): ?>
                            <!-- Clinic Address -->
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="text-primary-custom mb-2">Clinic Address</h6>
                                        <p class="text-muted mb-0"><?php echo nl2br(htmlspecialchars($doctorProfile['clinic_address'])); ?></p>
                                        <a href="#map" class="btn btn-sm btn-outline-primary mt-2" onclick="scrollToMap()">
                                            <i class="fas fa-directions me-1"></i>Get Directions
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Phone -->
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="text-primary-custom mb-2">Phone</h6>
                                        <p class="mb-2">
                                            <a href="tel:<?php echo $doctorProfile['phone']; ?>" class="text-decoration-none text-dark fw-bold">
                                                <?php echo formatPhone($doctorProfile['phone']); ?>
                                            </a>
                                        </p>
                                        <small class="text-muted">Available during office hours</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="text-primary-custom mb-2">Email</h6>
                                        <p class="mb-2">
                                            <a href="mailto:<?php echo $doctorProfile['email']; ?>" class="text-decoration-none text-dark fw-bold">
                                                <?php echo htmlspecialchars($doctorProfile['email']); ?>
                                            </a>
                                        </p>
                                        <small class="text-muted">We reply within 24 hours</small>
                                    </div>
                                </div>
                            </div>

                            <!-- WhatsApp -->
                            <?php if ($doctorProfile['whatsapp']): ?>
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="contact-icon bg-success me-3">
                                        <i class="fab fa-whatsapp"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="text-success mb-2">WhatsApp</h6>
                                        <p class="mb-2">
                                            <a href="<?php echo generateWhatsAppURL($doctorProfile['whatsapp'], 'Hello, I have a question about your services.'); ?>"
                                               class="text-decoration-none text-dark fw-bold" target="_blank">
                                                <?php echo formatPhone($doctorProfile['whatsapp']); ?>
                                            </a>
                                        </p>
                                        <small class="text-muted">Quick messaging available</small>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Office Hours -->
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="text-primary-custom mb-2">Office Hours</h6>
                                        <div class="office-hours">
                                            <div class="d-flex justify-content-between mb-1">
                                                <span>Monday - Thursday:</span>
                                                <span class="fw-bold">9:00 AM - 6:00 PM</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-1">
                                                <span>Saturday:</span>
                                                <span class="fw-bold">9:00 AM - 2:00 PM</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>Friday & Sunday:</span>
                                                <span class="text-danger fw-bold">Closed</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card-footer bg-light border-0">
                            <h6 class="text-primary-custom mb-3">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                            <div class="d-grid gap-2">
                                <a href="appointment.php" class="btn btn-primary-custom">
                                    <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                                </a>
                                <?php if ($doctorProfile && $doctorProfile['whatsapp']): ?>
                                <a href="<?php echo generateWhatsAppURL($doctorProfile['whatsapp'], getSetting('whatsapp_message', 'Hello, I would like to schedule an appointment.')); ?>"
                                   class="btn btn-success" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>Chat on WhatsApp
                                </a>
                                <?php endif; ?>
                                <?php if ($doctorProfile): ?>
                                <a href="tel:<?php echo $doctorProfile['phone']; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-phone me-2"></i>Call Now
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Enhanced Map Section -->
<section class="py-5 bg-light" id="map">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h3 class="display-6 fw-bold text-primary-custom">Find Our Location</h3>
            <p class="lead text-muted">Visit us at our clinic for in-person consultation</p>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="map-container" data-aos="fade-up" data-aos-delay="200">
                    <?php
                    $mapEmbed = getSetting('google_maps_embed', '');
                    if ($mapEmbed):
                    ?>
                    <div class="ratio ratio-16x9 rounded shadow-lg overflow-hidden">
                        <?php echo $mapEmbed; ?>
                    </div>
                    <?php else: ?>
                    <!-- Default Map Placeholder with Sample Embed -->
                    <div class="ratio ratio-16x9 rounded shadow-lg overflow-hidden">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3651.0977!2d90.4125181!3d23.7808875!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3755c7d8542ddb2b%3A0x4b2e4c9e7b8c5d6a!2sDhaka%2C%20Bangladesh!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                                width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                    <?php endif; ?>

                    <!-- Map Overlay Info -->
                    <div class="map-overlay-info mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-directions text-primary-custom fs-2 mb-3"></i>
                                        <h5 class="text-primary-custom">Get Directions</h5>
                                        <p class="text-muted mb-3">Navigate to our clinic easily</p>
                                        <a href="https://maps.google.com/?q=<?php echo urlencode($doctorProfile['clinic_address'] ?? 'Dhaka, Bangladesh'); ?>"
                                           class="btn btn-primary-custom" target="_blank">
                                            <i class="fas fa-external-link-alt me-2"></i>Open in Google Maps
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-parking text-secondary-custom fs-2 mb-3"></i>
                                        <h5 class="text-secondary-custom">Parking Available</h5>
                                        <p class="text-muted mb-3">Free parking space for patients</p>
                                        <div class="d-flex justify-content-center">
                                            <span class="badge bg-success me-2">
                                                <i class="fas fa-car me-1"></i>Car Parking
                                            </span>
                                            <span class="badge bg-info">
                                                <i class="fas fa-motorcycle me-1"></i>Bike Parking
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Office Hours -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h3 class="text-center mb-4">Office Hours</h3>
                <?php if ($doctorProfile): ?>
                <?php $workSchedule = json_decode($doctorProfile['work_schedule'], true); ?>
                <div class="schedule-table">
                    <table class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th>Day</th>
                                <th>Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workSchedule as $day => $schedule): ?>
                            <tr>
                                <td><strong><?php echo getDayName($day); ?></strong></td>
                                <td>
                                    <?php if ($schedule === 'closed' || (is_array($schedule) && isset($schedule['closed']))): ?>
                                        <span class="text-muted">Closed</span>
                                    <?php else: ?>
                                        <?php echo formatTime($schedule['start']) . ' - ' . formatTime($schedule['end']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($schedule === 'closed' || (is_array($schedule) && isset($schedule['closed']))): ?>
                                        <span class="badge bg-danger">Closed</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Open</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php 
$inlineJS = "
// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const phone = document.getElementById('phone').value;
    
    if (phone && phone.trim() !== '') {
        const phoneRegex = /^(\+880|880|0)?1[3-9]\d{8}$/;
        if (!phoneRegex.test(phone.replace(/[^0-9+]/g, ''))) {
            e.preventDefault();
            alert('Please enter a valid Bangladesh phone number or leave it empty');
            return false;
        }
    }
});
";
?>

<?php include 'includes/footer.php'; ?>
