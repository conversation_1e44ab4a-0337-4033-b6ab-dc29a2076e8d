/**
 * Comprehensive Validation and Testing Script
 * Ensures all features are working properly across the website
 */

class WebsiteValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.passed = [];
        this.init();
    }
    
    init() {
        this.validateThemeSystem();
        this.validateResponsiveDesign();
        this.validateAccessibility();
        this.validateForms();
        this.validateAnimations();
        this.validatePerformance();
        this.generateReport();
    }
    
    /**
     * Validate theme system functionality
     */
    validateThemeSystem() {
        const themeToggle = document.getElementById('themeToggle');
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        
        if (!themeToggle) {
            this.errors.push('Theme toggle button not found');
        } else {
            this.passed.push('Theme toggle button exists');
        }
        
        // Test theme switching
        if (themeToggle) {
            const originalTheme = currentTheme;
            themeToggle.click();
            
            setTimeout(() => {
                const newTheme = document.documentElement.getAttribute('data-theme');
                if (newTheme !== originalTheme) {
                    this.passed.push('Theme switching works correctly');
                } else {
                    this.errors.push('Theme switching not working');
                }
                
                // Restore original theme
                themeToggle.click();
            }, 100);
        }
        
        // Check localStorage persistence
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            this.passed.push('Theme preference is saved in localStorage');
        } else {
            this.warnings.push('Theme preference not found in localStorage');
        }
    }
    
    /**
     * Validate responsive design
     */
    validateResponsiveDesign() {
        const viewports = [
            { width: 320, height: 568, name: 'Mobile Small' },
            { width: 375, height: 667, name: 'Mobile Medium' },
            { width: 768, height: 1024, name: 'Tablet' },
            { width: 1024, height: 768, name: 'Desktop Small' },
            { width: 1920, height: 1080, name: 'Desktop Large' }
        ];
        
        const navbar = document.querySelector('.navbar');
        const cards = document.querySelectorAll('.card');
        
        if (navbar) {
            this.passed.push('Navigation bar exists');
            
            // Check if navbar is responsive
            if (navbar.querySelector('.navbar-toggler')) {
                this.passed.push('Mobile navigation toggle exists');
            } else {
                this.warnings.push('Mobile navigation toggle not found');
            }
        } else {
            this.errors.push('Navigation bar not found');
        }
        
        // Check card responsiveness
        if (cards.length > 0) {
            this.passed.push(`Found ${cards.length} cards for responsive testing`);
        } else {
            this.warnings.push('No cards found for responsive testing');
        }
    }
    
    /**
     * Validate accessibility features
     */
    validateAccessibility() {
        // Check ARIA labels
        const buttonsWithoutAria = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
        const iconsOnly = Array.from(buttonsWithoutAria).filter(btn => {
            return btn.querySelector('i') && !btn.textContent.trim();
        });
        
        if (iconsOnly.length === 0) {
            this.passed.push('All icon buttons have ARIA labels');
        } else {
            this.warnings.push(`${iconsOnly.length} icon buttons missing ARIA labels`);
        }
        
        // Check skip links
        const skipLinks = document.getElementById('skip-links');
        if (skipLinks) {
            this.passed.push('Skip links for screen readers exist');
        } else {
            this.warnings.push('Skip links not found');
        }
        
        // Check focus management
        const focusableElements = document.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (focusableElements.length > 0) {
            this.passed.push(`${focusableElements.length} focusable elements found`);
        }
        
        // Check color contrast (basic check)
        const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
        if (primaryColor) {
            this.passed.push('CSS custom properties are working');
        } else {
            this.errors.push('CSS custom properties not working');
        }
    }
    
    /**
     * Validate form functionality
     */
    validateForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach((form, index) => {
            const requiredFields = form.querySelectorAll('[required]');
            const submitButton = form.querySelector('button[type="submit"]');
            
            if (submitButton) {
                this.passed.push(`Form ${index + 1}: Submit button exists`);
            } else {
                this.warnings.push(`Form ${index + 1}: No submit button found`);
            }
            
            if (requiredFields.length > 0) {
                this.passed.push(`Form ${index + 1}: ${requiredFields.length} required fields found`);
            }
            
            // Check CSRF token
            const csrfToken = form.querySelector('input[name="csrf_token"]');
            if (csrfToken) {
                this.passed.push(`Form ${index + 1}: CSRF protection enabled`);
            } else {
                this.warnings.push(`Form ${index + 1}: CSRF token not found`);
            }
        });
        
        if (forms.length === 0) {
            this.warnings.push('No forms found on this page');
        }
    }
    
    /**
     * Validate animations and transitions
     */
    validateAnimations() {
        // Check AOS library
        if (typeof AOS !== 'undefined') {
            this.passed.push('AOS animation library loaded');
        } else {
            this.warnings.push('AOS animation library not loaded');
        }
        
        // Check CSS animations
        const animatedElements = document.querySelectorAll('[data-aos], .animate-in, .stagger-animation');
        if (animatedElements.length > 0) {
            this.passed.push(`${animatedElements.length} animated elements found`);
        } else {
            this.warnings.push('No animated elements found');
        }
        
        // Check transition properties
        const elementsWithTransitions = document.querySelectorAll('*');
        let transitionCount = 0;
        
        elementsWithTransitions.forEach(el => {
            const style = getComputedStyle(el);
            if (style.transition !== 'all 0s ease 0s') {
                transitionCount++;
            }
        });
        
        if (transitionCount > 0) {
            this.passed.push(`${transitionCount} elements have CSS transitions`);
        }
    }
    
    /**
     * Validate performance features
     */
    validatePerformance() {
        // Check lazy loading
        const lazyImages = document.querySelectorAll('img[loading="lazy"]');
        if (lazyImages.length > 0) {
            this.passed.push(`${lazyImages.length} images have lazy loading`);
        } else {
            this.warnings.push('No lazy loading images found');
        }
        
        // Check service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(registrations => {
                if (registrations.length > 0) {
                    this.passed.push('Service worker registered');
                } else {
                    this.warnings.push('Service worker not registered');
                }
            });
        } else {
            this.warnings.push('Service worker not supported');
        }
        
        // Check resource preloading
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        if (preloadLinks.length > 0) {
            this.passed.push(`${preloadLinks.length} resources are preloaded`);
        } else {
            this.warnings.push('No resource preloading found');
        }
    }
    
    /**
     * Generate validation report
     */
    generateReport() {
        console.group('🔍 Website Validation Report');
        
        if (this.passed.length > 0) {
            console.group('✅ Passed Tests');
            this.passed.forEach(test => console.log(`✓ ${test}`));
            console.groupEnd();
        }
        
        if (this.warnings.length > 0) {
            console.group('⚠️ Warnings');
            this.warnings.forEach(warning => console.warn(`⚠ ${warning}`));
            console.groupEnd();
        }
        
        if (this.errors.length > 0) {
            console.group('❌ Errors');
            this.errors.forEach(error => console.error(`✗ ${error}`));
            console.groupEnd();
        }
        
        const total = this.passed.length + this.warnings.length + this.errors.length;
        const score = Math.round((this.passed.length / total) * 100);
        
        console.log(`📊 Overall Score: ${score}% (${this.passed.length}/${total} tests passed)`);
        console.groupEnd();
        
        // Display report in UI if in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            this.displayUIReport(score);
        }
    }
    
    /**
     * Display validation report in UI
     */
    displayUIReport(score) {
        const reportDiv = document.createElement('div');
        reportDiv.id = 'validation-report';
        reportDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #007BFF;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            max-width: 300px;
            font-family: monospace;
            font-size: 12px;
        `;
        
        reportDiv.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                <strong>Validation Report</strong>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 16px; cursor: pointer;">×</button>
            </div>
            <div>Score: <strong style="color: ${score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red'}">${score}%</strong></div>
            <div>Passed: <span style="color: green">${this.passed.length}</span></div>
            <div>Warnings: <span style="color: orange">${this.warnings.length}</span></div>
            <div>Errors: <span style="color: red">${this.errors.length}</span></div>
            <div style="margin-top: 10px; font-size: 10px; color: #666;">
                Check console for details
            </div>
        `;
        
        document.body.appendChild(reportDiv);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (reportDiv.parentElement) {
                reportDiv.remove();
            }
        }, 10000);
    }
}

// Run validation when page loads (only in development)
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setTimeout(() => {
            new WebsiteValidator();
        }, 2000); // Wait for other scripts to load
    }
});
