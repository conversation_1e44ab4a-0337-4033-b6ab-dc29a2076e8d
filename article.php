<?php
require_once 'includes/init.php';
require_once 'models/Article.php';
require_once 'models/DoctorProfile.php';

$articleModel = new Article();
$doctorProfileModel = new DoctorProfile();
$doctorProfile = $doctorProfileModel->getActiveProfile();

// Get article slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: articles.php');
    exit;
}

// Get article by slug
$article = $articleModel->getArticleBySlug($slug);

if (!$article) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$pageTitle = $article['meta_title'] ?: $article['title'];
$metaDescription = $article['meta_description'] ?: $article['excerpt'];

// Get related articles
$relatedArticles = [];
if ($article['category']) {
    $relatedArticles = $articleModel->getPublishedArticlesByCategory($article['category'], 3);
    // Remove current article from related articles
    $relatedArticles = array_filter($relatedArticles, function($related) use ($article) {
        return $related['id'] !== $article['id'];
    });
}

include 'includes/header.php';
?>

<!-- Article Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <?php if ($article['category']): ?>
                <div class="mb-3">
                    <span class="badge bg-light text-primary fs-6 px-3 py-2">
                        <?php echo htmlspecialchars($article['category']); ?>
                    </span>
                </div>
                <?php endif; ?>
                
                <h1 class="display-5 fw-bold mb-4"><?php echo htmlspecialchars($article['title']); ?></h1>
                
                <?php if ($article['excerpt']): ?>
                <p class="lead mb-4"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                <?php endif; ?>
                
                <div class="d-flex justify-content-center align-items-center gap-4 text-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-md me-2"></i>
                        <span><?php echo htmlspecialchars($article['author_name'] ?: 'Dr. ' . $doctorProfile['full_name']); ?></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar me-2"></i>
                        <span><?php echo date('M j, Y', strtotime($article['published_at'] ?: $article['created_at'])); ?></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock me-2"></i>
                        <span><?php echo ceil(str_word_count(strip_tags($article['content'])) / 200); ?> min read</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Article Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Featured Image -->
                <?php if ($article['featured_image']): ?>
                <div class="article-featured-image mb-5" data-aos="fade-up">
                    <img src="<?php echo htmlspecialchars($article['featured_image']); ?>" 
                         alt="<?php echo htmlspecialchars($article['title']); ?>" 
                         class="img-fluid rounded shadow-lg">
                </div>
                <?php endif; ?>
                
                <!-- Article Content -->
                <div class="article-content" data-aos="fade-up" data-aos-delay="200">
                    <?php echo nl2br(htmlspecialchars($article['content'])); ?>
                </div>
                
                <!-- Tags -->
                <?php if ($article['tags']): ?>
                <div class="article-tags mt-5 pt-4 border-top" data-aos="fade-up" data-aos-delay="400">
                    <h6 class="mb-3">Tags:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <?php 
                        $tags = explode(',', $article['tags']);
                        foreach ($tags as $tag): 
                        ?>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars(trim($tag)); ?>
                        </span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Share Buttons -->
                <div class="article-share mt-5 pt-4 border-top" data-aos="fade-up" data-aos-delay="600">
                    <h6 class="mb-3">Share this article:</h6>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(BASE_URL . 'article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f me-2"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(BASE_URL . 'article.php?slug=' . $article['slug']); ?>&text=<?php echo urlencode($article['title']); ?>" 
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter me-2"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(BASE_URL . 'article.php?slug=' . $article['slug']); ?>" 
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in me-2"></i>LinkedIn
                        </a>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                            <i class="fas fa-link me-2"></i>Copy Link
                        </button>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="article-navigation mt-5 pt-4 border-top" data-aos="fade-up" data-aos-delay="800">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="articles.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Articles
                            </a>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="contact.php" class="btn btn-primary">
                                <i class="fas fa-comments me-2"></i>Ask a Question
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Articles -->
<?php if (!empty($relatedArticles)): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h3 class="display-6 fw-bold text-primary-custom">Related Articles</h3>
            <p class="lead text-muted">You might also be interested in these articles</p>
        </div>
        
        <div class="row">
            <?php foreach (array_slice($relatedArticles, 0, 3) as $index => $related): ?>
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 200; ?>">
                <div class="card h-100 border-0 shadow-sm">
                    <?php if ($related['featured_image']): ?>
                    <img src="<?php echo htmlspecialchars($related['featured_image']); ?>" 
                         alt="<?php echo htmlspecialchars($related['title']); ?>" 
                         class="card-img-top"
                         style="height: 200px; object-fit: cover;">
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="article.php?slug=<?php echo htmlspecialchars($related['slug']); ?>" 
                               class="text-decoration-none">
                                <?php echo htmlspecialchars($related['title']); ?>
                            </a>
                        </h5>
                        <p class="card-text text-muted">
                            <?php echo htmlspecialchars(substr($related['excerpt'] ?: strip_tags($related['content']), 0, 100)); ?>...
                        </p>
                    </div>
                    
                    <div class="card-footer bg-transparent border-0">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date('M j, Y', strtotime($related['published_at'] ?: $related['created_at'])); ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<script>
function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}
</script>

<?php include 'includes/footer.php'; ?>
