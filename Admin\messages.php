<?php
require_once '../includes/init.php';
require_once '../models/Message.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'Message Management';
$messageModel = new Message();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!Security::validateCSRFToken($_POST['csrf_token'])) {
        $_SESSION['flash_message'] = 'Invalid security token.';
        $_SESSION['flash_type'] = 'danger';
        header('Location: messages.php');
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_read') {
        $messageId = intval($_POST['message_id']);
        if ($messageModel->markAsRead($messageId)) {
            $_SESSION['flash_message'] = 'Message marked as read.';
            $_SESSION['flash_type'] = 'success';
        }
    } elseif ($action === 'mark_replied') {
        $messageId = intval($_POST['message_id']);
        if ($messageModel->markAsReplied($messageId)) {
            $_SESSION['flash_message'] = 'Message marked as replied.';
            $_SESSION['flash_type'] = 'success';
        }
    } elseif ($action === 'delete') {
        $messageId = intval($_POST['message_id']);
        if ($messageModel->deleteMessage($messageId)) {
            $_SESSION['flash_message'] = 'Message deleted successfully.';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'Failed to delete message.';
            $_SESSION['flash_type'] = 'danger';
        }
    } elseif ($action === 'bulk_action') {
        $messageIds = $_POST['message_ids'] ?? [];
        $bulkAction = $_POST['bulk_action_type'] ?? '';
        
        if (!empty($messageIds)) {
            if ($bulkAction === 'mark_read') {
                $messageModel->bulkUpdateStatus($messageIds, 'read');
                $_SESSION['flash_message'] = 'Messages marked as read.';
                $_SESSION['flash_type'] = 'success';
            } elseif ($bulkAction === 'mark_replied') {
                $messageModel->bulkUpdateStatus($messageIds, 'replied');
                $_SESSION['flash_message'] = 'Messages marked as replied.';
                $_SESSION['flash_type'] = 'success';
            } elseif ($bulkAction === 'delete') {
                $messageModel->bulkDeleteMessages($messageIds);
                $_SESSION['flash_message'] = 'Messages deleted successfully.';
                $_SESSION['flash_type'] = 'success';
            }
        }
    }
    
    header('Location: messages.php');
    exit;
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Get messages based on filters
if ($status === 'all') {
    $messages = $messageModel->getAllMessages();
} else {
    $messages = $messageModel->getMessagesByStatus($status);
}

// Filter by search term if provided
if ($search) {
    $messages = $messageModel->searchMessages($search);
}

// Get message statistics
$stats = $messageModel->getMessageStats();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-envelope me-2"></i>Message Management</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="refreshMessages()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_messages']; ?></h4>
                                    <p class="mb-0">Total Messages</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['unread_messages']; ?></h4>
                                    <p class="mb-0">Unread</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope-open fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['replied_messages']; ?></h4>
                                    <p class="mb-0">Replied</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-reply fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['today_messages']; ?></h4>
                                    <p class="mb-0">Today</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Messages</option>
                                <option value="unread" <?php echo $status === 'unread' ? 'selected' : ''; ?>>Unread</option>
                                <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>Read</option>
                                <option value="replied" <?php echo $status === 'replied' ? 'selected' : ''; ?>>Replied</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by name, email, or message content..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Messages Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Messages</h5>
                        <div class="d-flex gap-2">
                            <select id="bulkActionSelect" class="form-select form-select-sm" style="width: auto;">
                                <option value="">Bulk Actions</option>
                                <option value="mark_read">Mark as Read</option>
                                <option value="mark_replied">Mark as Replied</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" onclick="applyBulkAction()">Apply</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($messages)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5>No messages found</h5>
                            <p class="text-muted">
                                <?php if ($search || $status !== 'all'): ?>
                                    No messages match your current filters.
                                <?php else: ?>
                                    No messages have been received yet.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <form id="bulkForm" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="bulk_action">
                            <input type="hidden" name="bulk_action_type" value="">
                            
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="30">
                                                <input type="checkbox" id="selectAll" class="form-check-input">
                                            </th>
                                            <th>From</th>
                                            <th>Subject</th>
                                            <th>Message</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($messages as $message): ?>
                                        <tr class="<?php echo $message['is_read'] == 0 ? 'table-warning' : ''; ?>">
                                            <td>
                                                <input type="checkbox" name="message_ids[]" value="<?php echo $message['id']; ?>" class="form-check-input message-checkbox">
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($message['name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($message['email']); ?></small>
                                                <?php if ($message['phone']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($message['phone']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($message['subject'] ?: 'No Subject'); ?>
                                            </td>
                                            <td>
                                                <div class="message-preview">
                                                    <?php echo htmlspecialchars(substr($message['message'], 0, 100)); ?>
                                                    <?php if (strlen($message['message']) > 100): ?>...<?php endif; ?>
                                                </div>
                                                <button class="btn btn-link btn-sm p-0" onclick="viewMessage(<?php echo $message['id']; ?>)">
                                                    View Full Message
                                                </button>
                                            </td>
                                            <td>
                                                <?php if ($message['is_read'] == 0): ?>
                                                    <span class="badge bg-warning">Unread</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">Read</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y H:i', strtotime($message['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($message['is_read'] == 0): ?>
                                                        <button class="btn btn-outline-info" onclick="markAsRead(<?php echo $message['id']; ?>)" title="Mark as Read">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-danger" onclick="deleteMessage(<?php echo $message['id']; ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message View Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageContent">
                <!-- Message content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="replyToMessage()">Reply</button>
            </div>
        </div>
    </div>
</div>

<script>
// Message management JavaScript
function viewMessage(id) {
    fetch(`../api/message.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.message;
                document.getElementById('messageContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>From:</strong> ${message.name}<br>
                            <strong>Email:</strong> ${message.email}<br>
                            ${message.phone ? `<strong>Phone:</strong> ${message.phone}<br>` : ''}
                            <strong>Subject:</strong> ${message.subject || 'No Subject'}<br>
                            <strong>Date:</strong> ${new Date(message.created_at).toLocaleString()}
                        </div>
                        <div class="col-md-6">
                            <strong>Status:</strong> <span class="badge bg-${message.status === 'unread' ? 'warning' : message.status === 'read' ? 'info' : 'success'}">${message.status}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="message-body">
                        <strong>Message:</strong><br>
                        ${message.message.replace(/\n/g, '<br>')}
                    </div>
                `;
                
                new bootstrap.Modal(document.getElementById('messageModal')).show();
                
                // Mark as read if unread
                if (message.status === 'unread') {
                    markAsRead(id);
                }
            }
        });
}

function markAsRead(id) {
    submitAction('mark_read', id);
}

function markAsReplied(id) {
    submitAction('mark_replied', id);
}

function deleteMessage(id) {
    if (confirm('Are you sure you want to delete this message?')) {
        submitAction('delete', id);
    }
}

function submitAction(action, messageId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
        <input type="hidden" name="action" value="${action}">
        <input type="hidden" name="message_id" value="${messageId}">
    `;
    document.body.appendChild(form);
    form.submit();
}

function applyBulkAction() {
    const action = document.getElementById('bulkActionSelect').value;
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    
    if (!action) {
        alert('Please select an action.');
        return;
    }
    
    if (checkboxes.length === 0) {
        alert('Please select at least one message.');
        return;
    }
    
    if (action === 'delete' && !confirm('Are you sure you want to delete the selected messages?')) {
        return;
    }
    
    document.querySelector('input[name="bulk_action_type"]').value = action;
    document.getElementById('bulkForm').submit();
}

function refreshMessages() {
    window.location.reload();
}

function replyToMessage() {
    // This would open an email client or internal reply system
    alert('Reply functionality would be implemented here.');
}

// Select all checkbox functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.message-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
