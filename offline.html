<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Doctor <PERSON></title>
    <style>
        :root {
            --primary-color: #007BFF;
            --secondary-color: #17A2B8;
            --white: #FFFFFF;
            --text-dark: #212529;
            --text-muted: #6C757D;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 5rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: var(--white);
        }
        
        .features {
            margin-top: 3rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            opacity: 0.8;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .feature-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .offline-icon {
                font-size: 4rem;
            }
            
            .features {
                text-align: center;
            }
            
            .feature-item {
                flex-direction: column;
                text-align: center;
            }
            
            .feature-icon {
                margin-right: 0;
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1>You're Offline</h1>
        <p>It looks like you're not connected to the internet. Don't worry, you can still access some features of our website.</p>
        
        <a href="/" class="retry-btn" onclick="window.location.reload()">
            🔄 Try Again
        </a>
        
        <div class="features">
            <div class="feature-item">
                <div class="feature-icon">📋</div>
                <div class="feature-text">
                    <div class="feature-title">Cached Content</div>
                    <div class="feature-desc">Previously viewed pages are still available</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📞</div>
                <div class="feature-text">
                    <div class="feature-title">Emergency Contact</div>
                    <div class="feature-desc">Call us directly for urgent medical needs</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">💾</div>
                <div class="feature-text">
                    <div class="feature-title">Form Data Saved</div>
                    <div class="feature-desc">Your form data will be submitted when you're back online</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Check for connection and reload when back online
        window.addEventListener('online', function() {
            window.location.reload();
        });
        
        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Check connection every 5 seconds
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
