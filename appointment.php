<?php
require_once 'includes/init.php';
require_once 'models/DoctorProfile.php';
require_once 'models/Appointment.php';

$pageTitle = 'Book Appointment';
$doctorProfileModel = new DoctorProfile();
$appointmentModel = new Appointment();
$doctorProfile = $doctorProfileModel->getActiveProfile();

$errors = [];
$success = false;

// Get available time slots from settings
$timeSlots = json_decode(getSetting('appointment_slots', '["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"]'), true);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!Security::verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid form submission. Please try again.';
    } else {
        // Sanitize input
        $data = Security::sanitizeInput($_POST);
        
        // Validate required fields
        if (empty($data['patient_name'])) {
            $errors[] = 'Patient name is required.';
        }
        
        if (empty($data['patient_phone'])) {
            $errors[] = 'Phone number is required.';
        } elseif (!Security::validatePhone($data['patient_phone'])) {
            $errors[] = 'Please enter a valid phone number.';
        }
        
        if (empty($data['patient_email'])) {
            $errors[] = 'Email address is required.';
        } elseif (!Security::validateEmail($data['patient_email'])) {
            $errors[] = 'Please enter a valid email address.';
        }
        
        if (empty($data['appointment_date'])) {
            $errors[] = 'Appointment date is required.';
        } else {
            $appointmentDate = $data['appointment_date'];
            $today = date('Y-m-d');
            
            if ($appointmentDate < $today) {
                $errors[] = 'Appointment date cannot be in the past.';
            }
            
            // Check if the selected date is more than 30 days in the future
            $maxDate = date('Y-m-d', strtotime('+30 days'));
            if ($appointmentDate > $maxDate) {
                $errors[] = 'Appointment date cannot be more than 30 days in the future.';
            }
        }
        
        if (empty($data['appointment_time'])) {
            $errors[] = 'Appointment time is required.';
        } elseif (!in_array($data['appointment_time'], $timeSlots)) {
            $errors[] = 'Please select a valid appointment time.';
        }
        
        // Check if time slot is available
        if (empty($errors) && !$appointmentModel->isTimeSlotAvailable($data['appointment_date'], $data['appointment_time'])) {
            $errors[] = 'The selected time slot is not available. Please choose another time.';
        }
        
        // If no errors, create appointment
        if (empty($errors)) {
            $appointmentData = [
                'patient_name' => $data['patient_name'],
                'patient_phone' => $data['patient_phone'],
                'patient_email' => $data['patient_email'],
                'appointment_date' => $data['appointment_date'],
                'appointment_time' => $data['appointment_time'],
                'reason_for_visit' => $data['reason_for_visit'] ?? null
            ];
            
            $appointmentId = $appointmentModel->createAppointment($appointmentData);
            
            if ($appointmentId) {
                $success = true;
                
                // Send confirmation email (optional)
                $subject = "Appointment Request Confirmation";
                $message = "
                <h2>Appointment Request Received</h2>
                <p>Dear {$data['patient_name']},</p>
                <p>Your appointment request has been received and is pending confirmation.</p>
                <p><strong>Appointment Details:</strong></p>
                <ul>
                    <li>Date: " . formatDate($data['appointment_date']) . "</li>
                    <li>Time: " . formatTime($data['appointment_time']) . "</li>
                    <li>Doctor: {$doctorProfile['full_name']}</li>
                </ul>
                <p>We will contact you shortly to confirm your appointment.</p>
                <p>Thank you for choosing our services.</p>
                ";
                
                sendEmail($data['patient_email'], $subject, $message);
                
                // Clear form data
                $_POST = [];
            } else {
                $errors[] = 'Failed to book appointment. Please try again.';
            }
        }
    }
}

$additionalCSS = [
    'https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css',
    'https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css'
];
$additionalJS = [
    'https://cdn.jsdelivr.net/npm/flatpickr',
    'assets/js/appointment.js'
];
?>

<?php include 'includes/header.php'; ?>

<!-- Enhanced Page Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="mb-4">
                    <i class="fas fa-calendar-check display-1 mb-3 opacity-75"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">Book Your <span class="gradient-text">Appointment</span></h1>
                <p class="lead mb-4">Schedule your consultation with <?php echo htmlspecialchars($doctorProfile['full_name']); ?></p>
                <div class="row text-center">
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-item">
                            <i class="fas fa-clock fs-2 mb-2"></i>
                            <h6>Quick Booking</h6>
                            <small class="opacity-75">Easy online scheduling</small>
                        </div>
                    </div>
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt fs-2 mb-2"></i>
                            <h6>Secure & Private</h6>
                            <small class="opacity-75">Your data is protected</small>
                        </div>
                    </div>
                    <div class="col-md-4" data-aos="fade-up" data-aos-delay="600">
                        <div class="feature-item">
                            <i class="fas fa-phone fs-2 mb-2"></i>
                            <h6>Instant Confirmation</h6>
                            <small class="opacity-75">Get notified immediately</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Appointment Form Section -->
<section class="py-5">
    <div class="container">
        <?php if ($success): ?>
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="alert alert-success text-center">
                    <i class="bi bi-check-circle-fill fs-1 text-success mb-3 d-block"></i>
                    <h4>Appointment Request Submitted!</h4>
                    <p>Thank you for your appointment request. We have received your information and will contact you shortly to confirm your appointment.</p>
                    <p><strong>What's Next?</strong></p>
                    <ul class="list-unstyled">
                        <li>✓ You will receive a confirmation email</li>
                        <li>✓ Our staff will call you within 24 hours</li>
                        <li>✓ Please arrive 15 minutes early for your appointment</li>
                    </ul>
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary me-2">Back to Home</a>
                        <a href="appointment.php" class="btn btn-outline-primary">Book Another Appointment</a>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <div class="card shadow-lg border-0" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header bg-gradient-primary text-white border-0">
                        <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Patient Information</h4>
                        <small class="opacity-75">Please fill in your details to book an appointment</small>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" id="appointmentForm" data-validate="true">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">

                            <!-- Step 1: Personal Information -->
                            <div class="form-step active" id="step1">
                                <h5 class="text-primary-custom mb-4">
                                    <i class="fas fa-user me-2"></i>Personal Information
                                </h5>
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="patient_name" name="patient_name"
                                                   value="<?php echo htmlspecialchars($_POST['patient_name'] ?? ''); ?>"
                                                   placeholder="Full Name" required>
                                            <label for="patient_name">
                                                <i class="fas fa-user me-2"></i>Full Name *
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="patient_phone" name="patient_phone"
                                                   value="<?php echo htmlspecialchars($_POST['patient_phone'] ?? ''); ?>"
                                                   placeholder="+880-1XXX-XXXXXX" required>
                                            <label for="patient_phone">
                                                <i class="fas fa-phone me-2"></i>Phone Number *
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="patient_email" name="patient_email"
                                               value="<?php echo htmlspecialchars($_POST['patient_email'] ?? ''); ?>"
                                               placeholder="Email Address" required>
                                        <label for="patient_email">
                                            <i class="fas fa-envelope me-2"></i>Email Address *
                                        </label>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="button" class="btn btn-primary-custom btn-lg" onclick="nextStep()">
                                        Next Step <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Date and Time Selection -->
                            <div class="form-step" id="step2">
                                <h5 class="text-primary-custom mb-4">
                                    <i class="fas fa-calendar-alt me-2"></i>Select Date & Time
                                </h5>

                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <label for="appointment_date" class="form-label fw-bold">
                                            <i class="fas fa-calendar me-2"></i>Preferred Date *
                                        </label>
                                        <input type="text" class="form-control form-control-lg" id="appointment_date" name="appointment_date"
                                               value="<?php echo htmlspecialchars($_POST['appointment_date'] ?? ''); ?>"
                                               placeholder="Select appointment date" required readonly>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Available dates are highlighted in the calendar
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <label for="appointment_time" class="form-label fw-bold">
                                            <i class="fas fa-clock me-2"></i>Preferred Time *
                                        </label>
                                        <div id="timeSlotContainer">
                                            <div class="text-muted text-center py-4">
                                                <i class="fas fa-calendar-day fs-1 mb-2 opacity-50"></i>
                                                <p>Please select a date first to see available time slots</p>
                                            </div>
                                        </div>
                                        <input type="hidden" id="appointment_time" name="appointment_time" required>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button type="button" class="btn btn-primary-custom btn-lg" onclick="nextStep()" disabled id="step2NextBtn">
                                        Next Step <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Additional Details & Confirmation -->
                            <div class="form-step" id="step3">
                                <h5 class="text-primary-custom mb-4">
                                    <i class="fas fa-clipboard-list me-2"></i>Additional Details
                                </h5>

                                <div class="mb-4">
                                    <label for="reason_for_visit" class="form-label fw-bold">
                                        <i class="fas fa-notes-medical me-2"></i>Reason for Visit (Optional)
                                    </label>
                                    <textarea class="form-control form-control-lg" id="reason_for_visit" name="reason_for_visit" rows="4"
                                              placeholder="Please describe your symptoms or reason for consultation (optional)"><?php echo htmlspecialchars($_POST['reason_for_visit'] ?? ''); ?></textarea>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        This helps the doctor prepare for your consultation
                                    </div>
                                </div>

                                <!-- Appointment Summary -->
                                <div class="card bg-light border-0 mb-4">
                                    <div class="card-header bg-primary-custom text-white">
                                        <h6 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Appointment Summary</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Patient:</strong>
                                                    <span id="summaryName">-</span>
                                                </div>
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Phone:</strong>
                                                    <span id="summaryPhone">-</span>
                                                </div>
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Email:</strong>
                                                    <span id="summaryEmail">-</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Date:</strong>
                                                    <span id="summaryDate">-</span>
                                                </div>
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Time:</strong>
                                                    <span id="summaryTime">-</span>
                                                </div>
                                                <div class="summary-item mb-3">
                                                    <strong class="text-primary-custom">Doctor:</strong>
                                                    <span><?php echo htmlspecialchars($doctorProfile['full_name']); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info border-0">
                                    <h6><i class="fas fa-info-circle me-2"></i>Important Information:</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li>Consultation fee: <strong>৳<?php echo number_format($doctorProfile['consultation_fee']); ?></strong></li>
                                                <li>Please arrive 15 minutes early</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li>Bring previous medical records</li>
                                                <li>Confirmation will be sent via phone/email</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button type="button" class="btn btn-success btn-lg" onclick="showConfirmationModal()">
                                        <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Progress Indicator -->
                <div class="progress-container mt-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-primary-custom" id="progressBar" style="width: 33.33%"></div>
                    </div>
                    <div class="d-flex justify-content-between mt-2">
                        <small class="text-muted step-label active" id="label1">
                            <i class="fas fa-user me-1"></i>Personal Info
                        </small>
                        <small class="text-muted step-label" id="label2">
                            <i class="fas fa-calendar me-1"></i>Date & Time
                        </small>
                        <small class="text-muted step-label" id="label3">
                            <i class="fas fa-check me-1"></i>Confirmation
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title" id="confirmationModalLabel">
                    <i class="fas fa-calendar-check me-2"></i>Confirm Your Appointment
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-md text-primary-custom" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-primary-custom">Dr. <?php echo htmlspecialchars($doctorProfile['full_name']); ?></h4>
                    <p class="text-muted"><?php echo htmlspecialchars($doctorProfile['specialization']); ?></p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="info-card p-3 bg-light rounded mb-3">
                            <h6 class="text-primary-custom mb-2">
                                <i class="fas fa-user me-2"></i>Patient Details
                            </h6>
                            <p class="mb-1"><strong>Name:</strong> <span id="modalName">-</span></p>
                            <p class="mb-1"><strong>Phone:</strong> <span id="modalPhone">-</span></p>
                            <p class="mb-0"><strong>Email:</strong> <span id="modalEmail">-</span></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card p-3 bg-light rounded mb-3">
                            <h6 class="text-primary-custom mb-2">
                                <i class="fas fa-calendar-alt me-2"></i>Appointment Details
                            </h6>
                            <p class="mb-1"><strong>Date:</strong> <span id="modalDate">-</span></p>
                            <p class="mb-1"><strong>Time:</strong> <span id="modalTime">-</span></p>
                            <p class="mb-0"><strong>Fee:</strong> ৳<?php echo number_format($doctorProfile['consultation_fee']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning border-0">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Please Note:</h6>
                    <ul class="mb-0">
                        <li>This is a booking request. Confirmation will be sent within 24 hours</li>
                        <li>Please arrive 15 minutes before your scheduled time</li>
                        <li>Bring any relevant medical records or previous test results</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-success btn-lg" onclick="submitAppointment()">
                    <i class="fas fa-check me-2"></i>Confirm Booking
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Doctor Info Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>You're booking with</h4>
                        <h3 class="text-primary-custom"><?php echo htmlspecialchars($doctorProfile['full_name']); ?></h3>
                        <p class="text-muted"><?php echo htmlspecialchars($doctorProfile['specialization']); ?></p>
                        <p><?php echo htmlspecialchars($doctorProfile['medical_degrees']); ?></p>
                        <p><i class="bi bi-award me-2"></i><?php echo $doctorProfile['years_experience']; ?> years of experience</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php 
$inlineJS = "
// Form validation
document.getElementById('appointmentForm').addEventListener('submit', function(e) {
    const phone = document.getElementById('patient_phone').value;
    const phoneRegex = /^(\+880|880|0)?1[3-9]\d{8}$/;
    
    if (!phoneRegex.test(phone.replace(/[^0-9+]/g, ''))) {
        e.preventDefault();
        alert('Please enter a valid Bangladesh phone number');
        return false;
    }
});

// Date picker restrictions
document.getElementById('appointment_date').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const day = selectedDate.getDay(); // 0 = Sunday, 6 = Saturday
    
    // Add any day-specific restrictions here based on work schedule
    // For now, just ensure it's not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        alert('Please select a future date');
        this.value = '';
    }
});
";
?>

<?php include 'includes/footer.php'; ?>
