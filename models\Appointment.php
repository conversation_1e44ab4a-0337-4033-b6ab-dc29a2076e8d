<?php
/**
 * Appointment Model
 * Handles appointment booking and management
 */

class Appointment {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Create new appointment
     */
    public function createAppointment($data) {
        try {
            $sql = "INSERT INTO appointments (patient_name, patient_phone, patient_email, appointment_date, appointment_time, reason_for_visit) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $this->db->execute($sql, [
                $data['patient_name'],
                $data['patient_phone'],
                $data['patient_email'],
                $data['appointment_date'],
                $data['appointment_time'],
                $data['reason_for_visit'] ?? null
            ]);
            
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Create appointment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get appointment by ID
     */
    public function getAppointmentById($id) {
        $sql = "SELECT * FROM appointments WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * Get all appointments with pagination
     */
    public function getAppointments($page = 1, $limit = 20, $status = null, $date = null) {
        $offset = ($page - 1) * $limit;
        $conditions = [];
        $params = [];
        
        if ($status) {
            $conditions[] = "status = ?";
            $params[] = $status;
        }
        
        if ($date) {
            $conditions[] = "appointment_date = ?";
            $params[] = $date;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        $sql = "SELECT * FROM appointments {$whereClause} 
                ORDER BY appointment_date DESC, appointment_time DESC 
                LIMIT {$limit} OFFSET {$offset}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get appointments count
     */
    public function getAppointmentsCount($status = null, $date = null) {
        $conditions = [];
        $params = [];
        
        if ($status) {
            $conditions[] = "status = ?";
            $params[] = $status;
        }
        
        if ($date) {
            $conditions[] = "appointment_date = ?";
            $params[] = $date;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        $sql = "SELECT COUNT(*) as count FROM appointments {$whereClause}";
        $result = $this->db->fetch($sql, $params);
        
        return $result['count'];
    }
    
    /**
     * Update appointment status
     */
    public function updateStatus($id, $status, $notes = null) {
        try {
            $sql = "UPDATE appointments SET status = ?, notes = ? WHERE id = ?";
            $this->db->execute($sql, [$status, $notes, $id]);
            return true;
        } catch (Exception $e) {
            error_log("Update appointment status error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reschedule appointment
     */
    public function rescheduleAppointment($id, $newDate, $newTime) {
        try {
            $sql = "UPDATE appointments SET appointment_date = ?, appointment_time = ? WHERE id = ?";
            $this->db->execute($sql, [$newDate, $newTime, $id]);
            return true;
        } catch (Exception $e) {
            error_log("Reschedule appointment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable($date, $time, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM appointments 
                WHERE appointment_date = ? AND appointment_time = ? AND status IN ('pending', 'approved')";
        $params = [$date, $time];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * Get appointments for a specific date
     */
    public function getAppointmentsByDate($date) {
        $sql = "SELECT * FROM appointments 
                WHERE appointment_date = ? 
                ORDER BY appointment_time ASC";
        return $this->db->fetchAll($sql, [$date]);
    }
    
    /**
     * Get upcoming appointments
     */
    public function getUpcomingAppointments($limit = 5) {
        $sql = "SELECT * FROM appointments 
                WHERE appointment_date >= CURDATE() AND status IN ('pending', 'approved')
                ORDER BY appointment_date ASC, appointment_time ASC 
                LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * Get appointment statistics
     */
    public function getAppointmentStats() {
        $stats = [];
        
        // Total appointments
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM appointments");
        $stats['total'] = $result['count'];
        
        // Pending appointments
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM appointments WHERE status = 'pending'");
        $stats['pending'] = $result['count'];
        
        // Today's appointments
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE()");
        $stats['today'] = $result['count'];
        
        // This month's appointments
        $result = $this->db->fetch("SELECT COUNT(*) as count FROM appointments WHERE MONTH(appointment_date) = MONTH(CURDATE()) AND YEAR(appointment_date) = YEAR(CURDATE())");
        $stats['this_month'] = $result['count'];
        
        return $stats;
    }
    
    /**
     * Delete appointment
     */
    public function deleteAppointment($id) {
        try {
            $sql = "DELETE FROM appointments WHERE id = ?";
            $this->db->execute($sql, [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Delete appointment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Search appointments
     */
    public function searchAppointments($query, $limit = 20) {
        $sql = "SELECT * FROM appointments 
                WHERE patient_name LIKE ? OR patient_phone LIKE ? OR patient_email LIKE ? 
                ORDER BY appointment_date DESC, appointment_time DESC 
                LIMIT ?";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $limit]);
    }
}
?>
