<?php
/**
 * Article API Endpoint
 * Handles AJAX requests for article management
 */

require_once '../includes/init.php';
require_once '../models/Article.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is authenticated (for admin operations)
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$articleModel = new Article();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single article
                $article = $articleModel->getArticleById($_GET['id']);
                if ($article) {
                    echo json_encode(['success' => true, 'article' => $article]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Article not found']);
                }
            } else {
                // Get all articles
                $articles = $articleModel->getAllArticles();
                echo json_encode(['success' => true, 'articles' => $articles]);
            }
            break;
            
        case 'POST':
            // Create new article
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
                break;
            }
            
            // Validate required fields
            if (empty($input['title']) || empty($input['content'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Title and content are required']);
                break;
            }
            
            $data = [
                'title' => trim($input['title']),
                'content' => trim($input['content']),
                'excerpt' => trim($input['excerpt'] ?? ''),
                'category' => trim($input['category'] ?? ''),
                'tags' => trim($input['tags'] ?? ''),
                'status' => $input['status'] ?? 'draft',
                'meta_title' => trim($input['meta_title'] ?? $input['title']),
                'meta_description' => trim($input['meta_description'] ?? $input['excerpt']),
                'author_id' => $_SESSION['admin_id']
            ];
            
            // Generate slug
            $data['slug'] = $articleModel->generateSlug($data['title']);
            
            if ($articleModel->createArticle($data)) {
                echo json_encode(['success' => true, 'message' => 'Article created successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to create article']);
            }
            break;
            
        case 'PUT':
            // Update article
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Article ID is required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
                break;
            }
            
            $data = [
                'title' => trim($input['title']),
                'content' => trim($input['content']),
                'excerpt' => trim($input['excerpt'] ?? ''),
                'category' => trim($input['category'] ?? ''),
                'tags' => trim($input['tags'] ?? ''),
                'status' => $input['status'] ?? 'draft',
                'meta_title' => trim($input['meta_title'] ?? $input['title']),
                'meta_description' => trim($input['meta_description'] ?? $input['excerpt'])
            ];
            
            // Generate slug
            $data['slug'] = $articleModel->generateSlug($data['title'], $_GET['id']);
            
            if ($articleModel->updateArticle($_GET['id'], $data)) {
                echo json_encode(['success' => true, 'message' => 'Article updated successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to update article']);
            }
            break;
            
        case 'DELETE':
            // Delete article
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Article ID is required']);
                break;
            }
            
            if ($articleModel->deleteArticle($_GET['id'])) {
                echo json_encode(['success' => true, 'message' => 'Article deleted successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to delete article']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
