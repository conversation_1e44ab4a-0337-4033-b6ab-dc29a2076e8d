<?php
require_once 'includes/init.php';
require_once 'models/Video.php';
require_once 'models/DoctorProfile.php';

$pageTitle = 'Health Education Videos';
$videoModel = new Video();
$doctorProfileModel = new DoctorProfile();
$doctorProfile = $doctorProfileModel->getActiveProfile();

// Get filter parameters
$category = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';

// Get videos based on filters
if ($category) {
    $videos = $videoModel->getVideosByCategory($category);
} else {
    $videos = $videoModel->getAllVideos();
}

// Filter by search term if provided
if ($search) {
    $videos = array_filter($videos, function($video) use ($search) {
        return stripos($video['title'], $search) !== false || 
               stripos($video['description'], $search) !== false ||
               stripos($video['tags'], $search) !== false;
    });
}

// Get all categories for filter dropdown
$categories = $videoModel->getCategories();

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="mb-4">
                    <i class="fas fa-video display-1 mb-3 opacity-75"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">Health Education <span class="gradient-text">Videos</span></h1>
                <p class="lead mb-4">Learn about health, wellness, and medical topics through our educational video library</p>
            </div>
        </div>
    </div>
</section>

<!-- Video Filters -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <form method="GET" class="d-flex flex-wrap gap-3 align-items-center">
                    <div class="flex-grow-1">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Search videos..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <select name="category" class="form-select">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat); ?>" 
                                        <?php echo $category === $cat ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                    
                    <?php if ($category || $search): ?>
                    <div class="flex-shrink-0">
                        <a href="videos.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Videos Grid -->
<section class="py-5" id="main-content">
    <div class="container">
        <?php if (empty($videos)): ?>
            <div class="text-center py-5" data-aos="fade-up">
                <i class="fas fa-video fa-4x text-muted mb-4"></i>
                <h3>No Videos Found</h3>
                <p class="text-muted mb-4">
                    <?php if ($search || $category): ?>
                        No videos match your current filters. Try adjusting your search criteria.
                    <?php else: ?>
                        We're working on adding educational videos. Please check back soon!
                    <?php endif; ?>
                </p>
                <a href="videos.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>View All Videos
                </a>
            </div>
        <?php else: ?>
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <?php echo count($videos); ?> Video<?php echo count($videos) !== 1 ? 's' : ''; ?> Found
                            <?php if ($category): ?>
                                <span class="badge bg-primary ms-2"><?php echo htmlspecialchars($category); ?></span>
                            <?php endif; ?>
                        </h4>
                        
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sort me-2"></i>Sort By
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'newest'])); ?>">Newest First</a></li>
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'oldest'])); ?>">Oldest First</a></li>
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'title'])); ?>">Title A-Z</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Videos Grid -->
            <div class="row">
                <?php foreach ($videos as $index => $video): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index % 6) * 100; ?>">
                    <div class="video-card card h-100 border-0 shadow-sm">
                        <div class="video-thumbnail position-relative">
                            <?php 
                            $youtubeId = $videoModel->extractYouTubeId($video['video_url']);
                            $thumbnailUrl = $video['thumbnail'] ?: ($youtubeId ? $videoModel->getYouTubeThumbnail($youtubeId) : 'assets/images/video-placeholder.jpg');
                            ?>
                            <img src="<?php echo htmlspecialchars($thumbnailUrl); ?>" 
                                 alt="<?php echo htmlspecialchars($video['title']); ?>" 
                                 class="card-img-top video-thumb"
                                 style="height: 200px; object-fit: cover;">
                            
                            <div class="video-overlay">
                                <button class="btn btn-primary btn-lg rounded-circle video-play-btn" 
                                        onclick="playVideo('<?php echo htmlspecialchars($video['video_url']); ?>', '<?php echo htmlspecialchars($video['title']); ?>')">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                            
                            <?php if ($video['duration']): ?>
                            <div class="video-duration">
                                <?php echo gmdate("i:s", $video['duration']); ?>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($video['category']): ?>
                            <div class="video-category">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($video['category']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($video['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($video['description'], 0, 120)); ?>
                                <?php if (strlen($video['description']) > 120): ?>...<?php endif; ?>
                            </p>
                            
                            <?php if ($video['tags']): ?>
                            <div class="video-tags">
                                <?php 
                                $tags = explode(',', $video['tags']);
                                foreach (array_slice($tags, 0, 3) as $tag): 
                                ?>
                                <span class="badge bg-light text-dark me-1"><?php echo htmlspecialchars(trim($tag)); ?></span>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-transparent border-0">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y', strtotime($video['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Video Modal -->
<div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalLabel">Video</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <iframe id="videoFrame" src="" frameborder="0" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
