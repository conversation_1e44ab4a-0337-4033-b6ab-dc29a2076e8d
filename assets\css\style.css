/* Doctor <PERSON><PERSON><PERSON> Website - Main Stylesheet */

:root {
    /* Primary Color Scheme - Bootstrap Blue */
    --primary-color: #007BFF;
    --primary-hover: #0056b3;
    --primary-light: rgba(0, 123, 255, 0.1);
    --primary-gradient: linear-gradient(135deg, #007BFF 0%, #17A2B8 100%);

    /* Secondary Color Scheme - Cyan/Teal */
    --secondary-color: #17A2B8;
    --secondary-hover: #138496;
    --secondary-light: rgba(23, 162, 184, 0.1);
    --secondary-gradient: linear-gradient(135deg, #17A2B8 0%, #007BFF 100%);

    /* Light Theme Colors */
    --bg-primary: #FFFFFF;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6C757D;
    --text-inverse: #ffffff;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --navbar-bg: rgba(255, 255, 255, 0.95);

    /* Shadows for Light Theme */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    /* Legacy Variables for Backward Compatibility */
    --white: var(--bg-primary);
    --light-color: var(--bg-secondary);
    --dark-color: #343a40;
    --text-dark: var(--text-primary);
    --text-muted: var(--text-secondary);
    --text-light: var(--text-inverse);

    /* Spacing System - 8px grid */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Border Radius */
    --border-radius: 8px;
    --border-radius-lg: 16px;

    /* Transitions */
    --transition: 0.3s ease-in-out;
    --transition-fast: 0.15s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-inverse: #212529;
    --border-color: #404040;
    --card-bg: #2d2d2d;
    --navbar-bg: rgba(26, 26, 26, 0.95);

    /* Shadows for Dark Theme */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.6);

    /* Adjusted Primary Colors for Dark Theme */
    --primary-light: rgba(0, 123, 255, 0.2);
    --secondary-light: rgba(23, 162, 184, 0.2);

    /* Legacy Variables for Dark Theme */
    --white: var(--bg-primary);
    --light-color: var(--bg-secondary);
    --text-dark: var(--text-primary);
    --text-muted: var(--text-secondary);
    --text-light: var(--text-inverse);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Roboto', 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    font-weight: 400;
    background-color: var(--bg-primary);
    transition: background-color var(--transition), color var(--transition);
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    transition: color var(--transition);
}

h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 600; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 500; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1rem; font-weight: 500; }

p {
    margin-bottom: var(--spacing-md);
    font-weight: 400;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.5;
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.text-secondary-custom {
    color: var(--secondary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.bg-secondary-custom {
    background-color: var(--secondary-color) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: all var(--transition);
    border: 2px solid transparent;
    font-family: 'Poppins', sans-serif;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.1rem;
    border-radius: var(--border-radius-lg);
}

.btn-primary-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-primary-custom:hover,
.btn-primary-custom:focus {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-hover);
    color: var(--white);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.5);
    animation: buttonPulse 0.6s ease-in-out;
}

@keyframes buttonPulse {
    0% { transform: translateY(-3px) scale(1.02); }
    50% { transform: translateY(-4px) scale(1.03); }
    100% { transform: translateY(-3px) scale(1.02); }
}

.btn-secondary-custom {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-secondary-custom:hover,
.btn-secondary-custom:focus {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.btn-outline-primary-custom {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary-custom:hover,
.btn-outline-primary-custom:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Enhanced Header Styles */
.navbar {
    padding: var(--spacing-md) 0;
    backdrop-filter: blur(10px);
    background-color: var(--navbar-bg) !important;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition);
}

.navbar-brand {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.75rem;
    color: var(--primary-color) !important;
    transition: all var(--transition);
}

.navbar-brand:hover {
    color: var(--primary-hover) !important;
    transform: scale(1.05);
}

.navbar-brand i {
    margin-right: var(--spacing-sm);
    font-size: 1.5rem;
}

.navbar-nav {
    gap: var(--spacing-sm);
}

.navbar-nav .nav-link {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    transition: all var(--transition);
    border-radius: var(--border-radius);
    color: var(--text-primary) !important;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

.navbar-nav .nav-link i {
    margin-right: var(--spacing-xs);
    font-size: 0.9rem;
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Theme Toggle Button */
.theme-toggle {
    position: relative;
    width: 50px;
    height: 26px;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50px;
    cursor: pointer;
    transition: all var(--transition);
    margin-left: var(--spacing-md);
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: var(--primary-gradient);
    border-radius: 50%;
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .theme-toggle::before {
    transform: translateX(24px);
    background: var(--secondary-gradient);
}

.theme-toggle-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    transition: all var(--transition);
    pointer-events: none;
}

.theme-toggle .sun-icon {
    left: 6px;
    color: #ffd700;
    opacity: 1;
}

.theme-toggle .moon-icon {
    right: 6px;
    color: #4a90e2;
    opacity: 0.5;
}

[data-theme="dark"] .theme-toggle .sun-icon {
    opacity: 0.5;
}

[data-theme="dark"] .theme-toggle .moon-icon {
    opacity: 1;
    color: #ffffff;
}

/* Theme Toggle in Mobile */
@media (max-width: 991px) {
    .theme-toggle {
        margin: var(--spacing-sm) 0;
    }
}

/* Theme Transition Effects */
.theme-transitioning * {
    transition: background-color var(--transition),
                color var(--transition),
                border-color var(--transition),
                box-shadow var(--transition) !important;
}

/* Theme-aware Components */
.bg-gradient-primary {
    background: var(--primary-gradient) !important;
    transition: background var(--transition);
}

.bg-gradient-secondary {
    background: var(--secondary-gradient) !important;
    transition: background var(--transition);
}

/* Enhanced Button Gradients for Dark Theme */
[data-theme="dark"] .btn-primary-custom {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

[data-theme="dark"] .btn-secondary-custom {
    background: var(--secondary-gradient);
    border-color: var(--secondary-color);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

/* Dark theme specific adjustments */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-theme="dark"] .hero-section::before {
    opacity: 0.1;
}

[data-theme="dark"] .profile-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .schedule-table th {
    background: var(--primary-gradient);
}

[data-theme="dark"] .nav-tabs .nav-link.active {
    background: var(--primary-gradient);
    color: var(--text-inverse);
}

/* Theme Animation Classes */
.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.animate-light {
    animation: lightThemeIn 0.5s ease-out;
}

.animate-dark {
    animation: darkThemeIn 0.5s ease-out;
}

@keyframes lightThemeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes darkThemeIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Enhanced Hero Section with Advanced Gradients */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, #6f42c1 100%);
    color: var(--white);
    padding: var(--spacing-xxl) 0 calc(var(--spacing-xxl) * 2);
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
    animation: gradientShift 10s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, #6f42c1 100%);
    }
    50% {
        background: linear-gradient(135deg, #6f42c1 0%, var(--primary-color) 50%, var(--secondary-color) 100%);
    }
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.15"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)" class="floating-1"/><circle cx="800" cy="300" r="150" fill="url(%23a)" class="floating-2"/><circle cx="400" cy="700" r="120" fill="url(%23a)" class="floating-3"/><circle cx="600" cy="100" r="80" fill="url(%23a)" class="floating-4"/><circle cx="100" cy="600" r="90" fill="url(%23a)" class="floating-5"/></svg>');
    background-size: cover;
    opacity: 0.4;
    animation: floatingBackground 20s ease-in-out infinite;
}

@keyframes floatingBackground {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(-1deg); }
    75% { transform: translateY(-15px) rotate(0.5deg); }
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: pulseGlow 8s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content .lead {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    margin-bottom: var(--spacing-xl);
    font-weight: 300;
    opacity: 0.95;
    line-height: 1.6;
}

.hero-image {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-image i {
    font-size: clamp(8rem, 15vw, 15rem);
    color: rgba(255, 255, 255, 0.15);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.hero-buttons .btn {
    min-width: 180px;
    justify-content: center;
}

/* Enhanced Profile Section */
.profile-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-top: -3rem;
    position: relative;
    z-index: 10;
    border: 1px solid var(--border-color);
}

.profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid var(--white);
    box-shadow: var(--shadow);
    transition: transform var(--transition);
}

.profile-image:hover {
    transform: scale(1.05);
}

.profile-info h2 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    font-size: 2.5rem;
}

.profile-info .text-muted {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
}

/* Icon Wrapper for Info Cards */
.icon-wrapper {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: 50%;
    flex-shrink: 0;
}

/* Hero Stats */
.stat-item {
    padding: var(--spacing-md);
}

.stat-item h3 {
    font-size: 2rem;
    color: var(--white);
    margin-bottom: var(--spacing-xs);
}

/* Hero Doctor Image */
.hero-doctor-image {
    position: relative;
}

.hero-doctor-image::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
    animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.5; }
}

/* Enhanced Schedule Table */
.schedule-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.schedule-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    font-weight: 600;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-family: 'Poppins', sans-serif;
}

.schedule-table td {
    border-color: var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
    transition: background-color var(--transition);
}

.schedule-table tbody tr:hover {
    background-color: var(--primary-light);
}

.schedule-table .badge {
    font-size: 0.875rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
}

/* Enhanced Tabs */
.nav-tabs {
    border-bottom: none;
    gap: var(--spacing-sm);
}

.nav-tabs .nav-link {
    border: 2px solid transparent;
    color: var(--text-muted);
    font-weight: 500;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition);
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.tab-content {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
    margin-top: var(--spacing-md);
    border: 1px solid var(--border-color);
}

/* Enhanced Cards with Theme Support */
.card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    background-color: var(--card-bg);
    color: var(--text-primary);
    transition: all var(--transition);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    animation: cardFloat 0.6s ease-in-out;
}

@keyframes cardFloat {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.03); }
    100% { transform: translateY(-8px) scale(1.02); }
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Contact Section */
.contact-info {
    background: var(--light-color);
    border-radius: 15px;
    padding: 2rem;
}

.contact-info .icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.25rem;
}

/* WhatsApp Button */
.whatsapp-btn {
    background-color: #25d366;
    border-color: #25d366;
    color: white;
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    background-color: #128c7e;
    border-color: #128c7e;
    color: white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .profile-image {
        width: 150px;
        height: 150px;
    }
    
    .whatsapp-btn {
        bottom: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* Loading Spinner */
.spinner-border-custom {
    color: var(--primary-color);
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

/* Badge Styles */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Appointment Booking Styles */
.form-step {
    display: none;
    animation: fadeInUp 0.5s ease-in-out;
}

.form-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress Indicator */
.progress-container {
    max-width: 400px;
    margin: 0 auto;
}

.step-label {
    font-weight: 500;
    transition: all var(--transition);
}

.step-label.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

/* Time Slots Grid */
.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.time-slot {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--text-dark);
    font-weight: 500;
    transition: all var(--transition);
    cursor: pointer;
    text-align: center;
}

.time-slot.available:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.time-slot.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.time-slot.disabled {
    background: var(--light-color);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.time-slot small {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Enhanced Form Controls */
.form-floating > .form-control {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    padding: 1rem 0.75rem;
    font-size: 1rem;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-floating > label {
    padding: 1rem 0.75rem;
    font-weight: 500;
    color: var(--text-muted);
}

/* Feature Items in Header */
.feature-item {
    padding: var(--spacing-md);
    transition: transform var(--transition);
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-sm);
}

/* Summary Cards */
.summary-item {
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
}

.info-card {
    transition: transform var(--transition);
}

.info-card:hover {
    transform: translateY(-2px);
}

/* Calendar Customization */
.appointment-calendar .flatpickr-day.available {
    background: var(--primary-light);
    color: var(--primary-color);
}

.appointment-calendar .flatpickr-day.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Modal Enhancements */
.modal-content {
    border-radius: var(--border-radius-lg);
}

.modal-header {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

/* Responsive Design for Appointment Form */
@media (max-width: 768px) {
    .time-slots-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .time-slot {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }

    .feature-item {
        margin-bottom: var(--spacing-md);
    }

    .form-floating > .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Enhanced Contact Page Styles */
.contact-info-enhanced .card {
    transition: transform var(--transition);
}

.contact-info-enhanced .card:hover {
    transform: translateY(-5px);
}

.contact-item {
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item:hover {
    background-color: var(--primary-light);
    margin: 0 -var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
    transition: all var(--transition);
}

.contact-icon.bg-success {
    background: #25d366 !important;
}

.contact-item:hover .contact-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow);
}

.office-hours {
    font-size: 0.9rem;
}

.office-hours .d-flex {
    padding: var(--spacing-xs) 0;
}

/* Map Enhancements */
.map-container {
    position: relative;
}

.map-overlay-info {
    margin-top: var(--spacing-lg);
}

.map-overlay-info .card {
    transition: transform var(--transition);
}

.map-overlay-info .card:hover {
    transform: translateY(-5px);
}

/* Contact Form Enhancements */
.contact-info {
    background: var(--light-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    height: fit-content;
    position: sticky;
    top: var(--spacing-lg);
}

/* Success Message Styling */
.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: none;
    border-radius: var(--border-radius-lg);
    color: #155724;
}

.alert-success i {
    color: #28a745;
}

/* Responsive Contact Adjustments */
@media (max-width: 992px) {
    .contact-info {
        position: static;
        margin-top: var(--spacing-lg);
    }

    .contact-item:hover {
        margin: 0;
        padding: var(--spacing-md) 0;
        background-color: transparent;
    }
}

@media (max-width: 768px) {
    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .office-hours .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .office-hours .fw-bold {
        margin-top: var(--spacing-xs);
    }
}

/* Accessibility Enhancements */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    border-radius: var(--border-radius);
    z-index: 9999;
    transition: top var(--transition);
}

.skip-link:focus {
    top: 6px;
    color: var(--white);
}

/* Keyboard Navigation Focus */
.keyboard-navigation *:focus {
    outline: 3px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* High Contrast Mode has been removed as per user request */

/* Performance Optimizations */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

/* Preload Critical Resources */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .sidebar,
    .btn,
    .high-contrast-toggle,
    #skip-links {
        display: none !important;
    }

    .container,
    .container-fluid {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
}

/* Responsive Images */
img {
    max-width: 100%;
    height: auto;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

/* Enhanced Focus Indicators */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.25);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .hero-section::before,
    .floating-animation,
    .pulse-glow {
        animation: none !important;
    }

    .time-slot:hover,
    .btn:hover,
    .card:hover {
        transform: none !important;
    }
}

/* Video Section Styles */
.video-card {
    transition: all var(--transition);
    overflow: hidden;
}

.video-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg) !important;
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumb {
    transition: transform var(--transition);
}

.video-card:hover .video-thumb {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition);
}

.video-card:hover .video-overlay {
    opacity: 1;
}

.video-play-btn {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient) !important;
    border: none;
    box-shadow: var(--shadow);
    transition: all var(--transition);
}

.video-play-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.video-play-btn i {
    margin-left: 3px; /* Optical alignment for play icon */
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 2px 6px;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 500;
}

.video-category {
    position: absolute;
    top: 8px;
    left: 8px;
}

.video-tags {
    margin-top: var(--spacing-sm);
}

.video-tags .badge {
    font-size: 0.7rem;
    padding: 4px 8px;
}

/* Video Modal Enhancements */
#videoModal .modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

#videoModal .modal-header {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
}

#videoModal .btn-close {
    filter: invert(1);
}

/* Responsive Video Adjustments */
@media (max-width: 768px) {
    .video-play-btn {
        width: 50px;
        height: 50px;
    }

    .video-duration {
        font-size: 0.65rem;
        padding: 1px 4px;
    }

    .video-card .card-body {
        padding: var(--spacing-md);
    }
}

/* Dark Theme Video Adjustments */
[data-theme="dark"] .video-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .video-overlay {
    background: rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .video-tags .badge.bg-light {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

/* Enhanced Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60% { content: '...'; }
    80%, 100% { content: ''; }
}

/* Page Transition Effects */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered Animation for Lists */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(30px);
    animation: staggerIn 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes staggerIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Micro-interactions */
.micro-bounce:hover {
    animation: microBounce 0.4s ease-in-out;
}

@keyframes microBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.micro-shake:hover {
    animation: microShake 0.5s ease-in-out;
}

@keyframes microShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Gradient Text Effects */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientTextShift 3s ease-in-out infinite;
}

@keyframes gradientTextShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Enhanced Form Focus Effects */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: scale(1.02);
    transition: all var(--transition);
}

.form-floating > .form-control:focus ~ label {
    color: var(--primary-color);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 4;
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    animation: progressRing 2s ease-in-out forwards;
}

@keyframes progressRing {
    to {
        stroke-dashoffset: 0;
    }
}
