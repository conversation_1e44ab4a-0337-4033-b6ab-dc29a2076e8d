<?php
/**
 * User Model
 * Handles user authentication and management
 */

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Authenticate user login
     */
    public function authenticate($username, $password) {
        try {
            $sql = "SELECT id, username, email, password_hash, full_name, role, is_active 
                    FROM users WHERE username = ? AND is_active = 1";
            $user = $this->db->fetch($sql, [$username]);
            
            if ($user && Security::verifyPassword($password, $user['password_hash'])) {
                // Update last login
                $this->updateLastLogin($user['id']);
                
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['full_name'] = $user['full_name'];
                
                Security::logSecurityEvent('User login', "User: {$username}");
                return $user;
            }
            
            Security::logSecurityEvent('Failed login attempt', "Username: {$username}");
            return false;
            
        } catch (Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $this->db->execute($sql, [$userId]);
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($id) {
        $sql = "SELECT id, username, email, full_name, role, is_active, last_login, created_at 
                FROM users WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * Get current logged-in user
     */
    public function getCurrentUser() {
        if (isset($_SESSION['user_id'])) {
            return $this->getUserById($_SESSION['user_id']);
        }
        return null;
    }
    
    /**
     * Create new user
     */
    public function createUser($data) {
        try {
            $sql = "INSERT INTO users (username, email, password_hash, full_name, role) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $passwordHash = Security::hashPassword($data['password']);
            
            $this->db->execute($sql, [
                $data['username'],
                $data['email'],
                $passwordHash,
                $data['full_name'],
                $data['role'] ?? 'admin'
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (Exception $e) {
            error_log("User creation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update user profile
     */
    public function updateProfile($userId, $data) {
        try {
            $sql = "UPDATE users SET email = ?, full_name = ? WHERE id = ?";
            $this->db->execute($sql, [$data['email'], $data['full_name'], $userId]);
            return true;
        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            $user = $this->getUserById($userId);
            if (!$user || !Security::verifyPassword($currentPassword, $user['password_hash'])) {
                return false;
            }
            
            $newPasswordHash = Security::hashPassword($newPassword);
            $sql = "UPDATE users SET password_hash = ? WHERE id = ?";
            $this->db->execute($sql, [$newPasswordHash, $userId]);
            
            Security::logSecurityEvent('Password changed', "User ID: {$userId}");
            return true;
            
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        Security::logSecurityEvent('User logout', "User: " . ($_SESSION['username'] ?? 'unknown'));
        
        session_unset();
        session_destroy();
        session_start();
    }
    
    /**
     * Check if username exists
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT id FROM users WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result !== false;
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT id FROM users WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result !== false;
    }
}
?>
