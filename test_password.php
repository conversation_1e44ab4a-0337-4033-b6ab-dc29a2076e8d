<?php
require_once 'includes/init.php';

// The password we're testing
$password = 'admin123';

// The hash from the database
$hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Verify the password
echo "Testing password: $password\n";
echo "Stored hash: $hash\n";

$result = password_verify($password, $hash);
echo "Password verification result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";

// Check if the password needs rehashing
if (password_needs_rehash($hash, PASSWORD_DEFAULT)) {
    echo "Password needs to be rehashed.\n";
    $newHash = password_hash($password, PASSWORD_DEFAULT);
    echo "New hash: $newHash\n";
}

// Check PHP version and hashing algorithm info
echo "\nPHP Version: " . phpversion() . "\n";
echo "Default hashing algorithm: " . PASSWORD_DEFAULT . "\n";

// Test if bcrypt is available
if (defined('PASSWORD_BCRYPT')) {
    echo "BCRYPT is available.\n";
    $bcryptHash = password_hash($password, PASSWORD_BCRYPT);
    echo "BCRYPT hash: $bcryptHash\n";
} else {
    echo "BCRYPT is NOT available.\n";
}
