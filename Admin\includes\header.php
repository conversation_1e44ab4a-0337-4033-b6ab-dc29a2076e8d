<?php
// Ensure user is logged in
Security::requireAdmin();

$currentUser = (new User())->getCurrentUser();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Admin Dashboard - Doctor <PERSON>lio</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Admin Custom CSS -->
    <link href="assets/css/admin.css" rel="stylesheet">
    <link href="assets/css/admin-enhanced.css" rel="stylesheet">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <div class="wrapper">
        <!-- Enhanced Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h4><i class="fas fa-user-md me-2"></i>Admin Panel</h4>
            </div>

            <ul class="list-unstyled components">
                <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'appointments.php' ? 'active' : ''; ?>">
                    <a href="appointments.php">
                        <i class="fas fa-calendar-check"></i>
                        <span>Appointments</span>
                        <?php
                        $appointmentModel = new Appointment();
                        $pendingCount = $appointmentModel->getAppointmentsCount('pending');
                        if ($pendingCount > 0):
                        ?>
                        <span class="badge bg-warning ms-2"><?php echo $pendingCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                
                <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'profile.php' ? 'active' : ''; ?>">
                    <a href="profile.php">
                        <i class="bi bi-person-circle"></i>
                        Doctor Profile
                    </a>
                </li>
                
                <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['videos.php', 'articles.php']) ? 'active' : ''; ?>">
                    <a href="#contentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="bi bi-folder"></i>
                        Content Management
                    </a>
                    <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['videos.php', 'articles.php']) ? 'show' : ''; ?>" id="contentSubmenu">
                        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'videos.php' ? 'active' : ''; ?>">
                            <a href="videos.php">
                                <i class="bi bi-play-circle"></i>
                                Videos
                            </a>
                        </li>
                        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'articles.php' ? 'active' : ''; ?>">
                            <a href="articles.php">
                                <i class="bi bi-file-text"></i>
                                Articles
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'messages.php' ? 'active' : ''; ?>">
                    <a href="messages.php">
                        <i class="bi bi-envelope"></i>
                        Messages
                        <?php
                        try {
                            $db = Database::getInstance();
                            $unreadCount = $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE is_read = 0");
                            if ($unreadCount && $unreadCount['count'] > 0):
                        ?>
                        <span class="badge bg-danger ms-2"><?php echo $unreadCount['count']; ?></span>
                        <?php 
                            endif;
                        } catch (Exception $e) {
                            // Ignore error for now
                        }
                        ?>
                    </a>
                </li>
                
                <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>">
                    <a href="settings.php">
                        <i class="bi bi-gear"></i>
                        Settings
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="d-flex align-items-center">
                        <div class="avatar">
                            <i class="bi bi-person-circle fs-3"></i>
                        </div>
                        <div class="ms-2">
                            <div class="fw-bold"><?php echo htmlspecialchars($currentUser['full_name']); ?></div>
                            <small class="text-muted"><?php echo ucfirst($currentUser['role']); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div id="content" class="content">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-outline-primary">
                        <i class="bi bi-list"></i>
                    </button>
                    
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="ms-3">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                            <?php if (isset($breadcrumbs)): ?>
                                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                    <?php if (isset($breadcrumb['url'])): ?>
                                        <li class="breadcrumb-item"><a href="<?php echo $breadcrumb['url']; ?>"><?php echo $breadcrumb['title']; ?></a></li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item active"><?php echo $breadcrumb['title']; ?></li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php elseif (isset($pageTitle)): ?>
                                <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                            <?php endif; ?>
                        </ol>
                    </nav>
                    
                    <div class="navbar-nav ms-auto">
                        <!-- Notifications -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-bell"></i>
                                <span class="badge bg-danger badge-sm">3</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">Notifications</h6></li>
                                <li><a class="dropdown-item" href="appointments.php">
                                    <i class="bi bi-calendar-check text-warning me-2"></i>
                                    New appointment request
                                </a></li>
                                <li><a class="dropdown-item" href="messages.php">
                                    <i class="bi bi-envelope text-info me-2"></i>
                                    New contact message
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
                            </ul>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle fs-5 me-2"></i>
                                <?php echo htmlspecialchars($currentUser['full_name']); ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="bi bi-person me-2"></i>Profile
                                </a></li>
                                <li><a class="dropdown-item" href="settings.php">
                                    <i class="bi bi-gear me-2"></i>Settings
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../index.php" target="_blank">
                                    <i class="bi bi-eye me-2"></i>View Website
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="bi bi-box-arrow-right me-2"></i>Logout
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="container-fluid p-4">
                <!-- Flash Messages -->
                <?php displayFlashMessage(); ?>
