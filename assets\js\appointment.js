/**
 * Enhanced Appointment Booking System
 * Handles multi-step form, date picker, time slot selection, and validation
 */

let currentStep = 1;
let selectedDate = null;
let selectedTime = null;
let flatpickrInstance = null;

// Available time slots (will be populated from server)
const timeSlots = [
    "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
    "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"
];

document.addEventListener('DOMContentLoaded', function() {
    initializeDatePicker();
    initializeFormValidation();
    updateProgressBar();
    
    // Initialize AOS for form steps
    if (typeof AOS !== 'undefined') {
        AOS.refresh();
    }
});

/**
 * Initialize Flatpickr date picker
 */
function initializeDatePicker() {
    const dateInput = document.getElementById('appointment_date');
    if (!dateInput) return;
    
    flatpickrInstance = flatpickr(dateInput, {
        theme: 'material_blue',
        minDate: 'today',
        maxDate: new Date().fp_incr(30),
        dateFormat: 'Y-m-d',
        disable: [
            // Disable Fridays (5) - adjust based on doctor's schedule
            function(date) {
                return date.getDay() === 5;
            }
        ],
        onChange: function(selectedDates, dateStr, instance) {
            selectedDate = dateStr;
            loadTimeSlots(dateStr);
            updateSummary();
        },
        onReady: function(selectedDates, dateStr, instance) {
            // Add custom styling
            instance.calendarContainer.classList.add('appointment-calendar');
        }
    });
}

/**
 * Load available time slots for selected date
 */
function loadTimeSlots(date) {
    const container = document.getElementById('timeSlotContainer');
    const nextBtn = document.getElementById('step2NextBtn');
    
    // Show loading state
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary-custom" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading available time slots...</p>
        </div>
    `;
    
    // Simulate API call to check availability
    setTimeout(() => {
        const slotsHTML = generateTimeSlotsHTML(date);
        container.innerHTML = slotsHTML;
        
        // Add click handlers to time slots
        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.addEventListener('click', function() {
                selectTimeSlot(this);
            });
        });
    }, 1000);
}

/**
 * Generate HTML for time slots
 */
function generateTimeSlotsHTML(date) {
    const dayOfWeek = new Date(date).getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    let html = '<div class="time-slots-grid">';
    
    timeSlots.forEach(slot => {
        const isAvailable = Math.random() > 0.3; // Simulate availability
        const isDisabled = isWeekend || !isAvailable;
        
        html += `
            <button type="button" class="time-slot ${isDisabled ? 'disabled' : 'available'}" 
                    data-time="${slot}" ${isDisabled ? 'disabled' : ''}>
                <i class="fas fa-clock me-2"></i>
                ${formatTime(slot)}
                ${!isAvailable ? '<small class="d-block">Booked</small>' : ''}
            </button>
        `;
    });
    
    html += '</div>';
    
    if (isWeekend) {
        html += `
            <div class="alert alert-warning mt-3">
                <i class="fas fa-info-circle me-2"></i>
                Weekend appointments may have limited availability. Please contact us directly.
            </div>
        `;
    }
    
    return html;
}

/**
 * Select time slot
 */
function selectTimeSlot(element) {
    // Remove previous selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.classList.remove('selected');
    });
    
    // Add selection to clicked slot
    element.classList.add('selected');
    selectedTime = element.dataset.time;
    
    // Update hidden input
    document.getElementById('appointment_time').value = selectedTime;
    
    // Enable next button
    document.getElementById('step2NextBtn').disabled = false;
    
    // Update summary
    updateSummary();
}

/**
 * Format time for display
 */
function formatTime(time) {
    const [hours, minutes] = time.split(':');
    const hour12 = hours % 12 || 12;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    return `${hour12}:${minutes} ${ampm}`;
}

/**
 * Navigate to next step
 */
function nextStep() {
    if (!validateCurrentStep()) {
        return;
    }
    
    // Hide current step
    document.getElementById(`step${currentStep}`).classList.remove('active');
    
    // Show next step
    currentStep++;
    document.getElementById(`step${currentStep}`).classList.add('active');
    
    // Update progress
    updateProgressBar();
    updateSummary();
    
    // Scroll to top of form
    document.getElementById('appointmentForm').scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
    });
}

/**
 * Navigate to previous step
 */
function prevStep() {
    // Hide current step
    document.getElementById(`step${currentStep}`).classList.remove('active');
    
    // Show previous step
    currentStep--;
    document.getElementById(`step${currentStep}`).classList.add('active');
    
    // Update progress
    updateProgressBar();
    
    // Scroll to top of form
    document.getElementById('appointmentForm').scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
    });
}

/**
 * Validate current step
 */
function validateCurrentStep() {
    const step = document.getElementById(`step${currentStep}`);
    const requiredFields = step.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Additional validation for step 1
    if (currentStep === 1) {
        const email = document.getElementById('patient_email');
        const phone = document.getElementById('patient_phone');
        
        if (email.value && !isValidEmail(email.value)) {
            showFieldError(email, 'Please enter a valid email address');
            isValid = false;
        }
        
        if (phone.value && !isValidPhone(phone.value)) {
            showFieldError(phone, 'Please enter a valid phone number');
            isValid = false;
        }
    }
    
    // Additional validation for step 2
    if (currentStep === 2) {
        if (!selectedDate || !selectedTime) {
            showToast('Please select both date and time', 'warning');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * Update progress bar
 */
function updateProgressBar() {
    const progressBar = document.getElementById('progressBar');
    const labels = document.querySelectorAll('.step-label');
    
    // Update progress bar width
    const progress = (currentStep / 3) * 100;
    progressBar.style.width = `${progress}%`;
    
    // Update step labels
    labels.forEach((label, index) => {
        if (index < currentStep) {
            label.classList.add('active');
        } else {
            label.classList.remove('active');
        }
    });
}

/**
 * Update appointment summary
 */
function updateSummary() {
    const name = document.getElementById('patient_name').value;
    const phone = document.getElementById('patient_phone').value;
    const email = document.getElementById('patient_email').value;
    
    // Update summary in step 3
    document.getElementById('summaryName').textContent = name || '-';
    document.getElementById('summaryPhone').textContent = phone || '-';
    document.getElementById('summaryEmail').textContent = email || '-';
    document.getElementById('summaryDate').textContent = selectedDate ? formatDate(selectedDate) : '-';
    document.getElementById('summaryTime').textContent = selectedTime ? formatTime(selectedTime) : '-';
    
    // Update modal
    document.getElementById('modalName').textContent = name || '-';
    document.getElementById('modalPhone').textContent = phone || '-';
    document.getElementById('modalEmail').textContent = email || '-';
    document.getElementById('modalDate').textContent = selectedDate ? formatDate(selectedDate) : '-';
    document.getElementById('modalTime').textContent = selectedTime ? formatTime(selectedTime) : '-';
}

/**
 * Format date for display
 */
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * Show confirmation modal
 */
function showConfirmationModal() {
    if (!validateCurrentStep()) {
        return;
    }
    
    updateSummary();
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    modal.show();
}

/**
 * Submit appointment form
 */
function submitAppointment() {
    const form = document.getElementById('appointmentForm');
    const submitBtn = document.querySelector('#confirmationModal .btn-success');
    
    // Show loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Booking...';
    submitBtn.disabled = true;
    
    // Submit form
    form.submit();
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const form = document.getElementById('appointmentForm');
    
    // Real-time validation
    form.addEventListener('input', function(e) {
        if (e.target.hasAttribute('required')) {
            if (e.target.value.trim()) {
                clearFieldError(e.target);
            }
        }
        
        // Update summary as user types
        if (currentStep >= 2) {
            updateSummary();
        }
    });
}
