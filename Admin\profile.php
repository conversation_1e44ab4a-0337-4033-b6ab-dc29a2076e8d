<?php
require_once '../includes/init.php';
require_once '../models/DoctorProfile.php';
require_once '../models/User.php';

// Check admin authentication
Security::requireAdmin();

$pageTitle = 'Doctor Profile Management';
$doctorProfileModel = new DoctorProfile();
$userModel = new User();

// Get current admin user
$adminUser = $userModel->getUserById($_SESSION['user_id']);
$doctorProfile = $doctorProfileModel->getActiveProfile();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!Security::verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['flash_message'] = 'Invalid security token.';
        $_SESSION['flash_type'] = 'danger';
        header('Location: profile.php');
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $data = [
            'full_name' => trim($_POST['full_name']),
            'specialization' => trim($_POST['specialization']),
            'qualification' => trim($_POST['qualification']),
            'years_experience' => intval($_POST['years_experience']),
            'bio' => trim($_POST['bio']),
            'phone' => trim($_POST['phone']),
            'email' => trim($_POST['email']),
            'whatsapp' => trim($_POST['whatsapp']),
            'clinic_address' => trim($_POST['clinic_address']),
            'consultation_fee' => floatval($_POST['consultation_fee']),
            'work_schedule' => json_encode($_POST['work_schedule'] ?? []),
            'languages' => trim($_POST['languages']),
            'awards' => trim($_POST['awards']),
            'certifications' => trim($_POST['certifications'])
        ];
        
        // Handle profile image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/profile/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $fileName = time() . '_' . $_FILES['profile_image']['name'];
            $uploadPath = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $uploadPath)) {
                $data['profile_image'] = 'uploads/profile/' . $fileName;
            }
        }
        
        if ($doctorProfile) {
            if ($doctorProfileModel->updateProfile($doctorProfile['id'], $data)) {
                $_SESSION['flash_message'] = 'Profile updated successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to update profile.';
                $_SESSION['flash_type'] = 'danger';
            }
        } else {
            $data['user_id'] = $_SESSION['admin_id'];
            if ($doctorProfileModel->createProfile($data)) {
                $_SESSION['flash_message'] = 'Profile created successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to create profile.';
                $_SESSION['flash_type'] = 'danger';
            }
        }
    } elseif ($action === 'update_password') {
        $currentPassword = $_POST['current_password'];
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];
        
        if ($newPassword !== $confirmPassword) {
            $_SESSION['flash_message'] = 'New passwords do not match.';
            $_SESSION['flash_type'] = 'danger';
        } elseif (strlen($newPassword) < 8) {
            $_SESSION['flash_message'] = 'Password must be at least 8 characters long.';
            $_SESSION['flash_type'] = 'danger';
        } elseif (!password_verify($currentPassword, $adminUser['password'])) {
            $_SESSION['flash_message'] = 'Current password is incorrect.';
            $_SESSION['flash_type'] = 'danger';
        } else {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            if ($userModel->updatePassword($_SESSION['admin_id'], $hashedPassword)) {
                $_SESSION['flash_message'] = 'Password updated successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to update password.';
                $_SESSION['flash_type'] = 'danger';
            }
        }
    }
    
    header('Location: profile.php');
    exit;
}

// Refresh data after potential updates
$doctorProfile = $doctorProfileModel->getActiveProfile();
$workSchedule = $doctorProfile ? json_decode($doctorProfile['work_schedule'], true) : [];

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-md me-2"></i>Doctor Profile Management</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#passwordModal">
                        <i class="fas fa-key me-2"></i>Change Password
                    </button>
                    <a href="../index.php" class="btn btn-outline-secondary" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>View Public Profile
                    </a>
                </div>
            </div>
            
            <!-- Profile Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">Profile Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <!-- Basic Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="full_name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($doctorProfile['full_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="specialization" class="form-label">Specialization *</label>
                                        <input type="text" class="form-control" id="specialization" name="specialization" 
                                               value="<?php echo htmlspecialchars($doctorProfile['specialization'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="qualification" class="form-label">Qualification</label>
                                        <input type="text" class="form-control" id="qualification" name="qualification" 
                                               value="<?php echo htmlspecialchars($doctorProfile['qualification'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="years_experience" class="form-label">Years of Experience</label>
                                        <input type="number" class="form-control" id="years_experience" name="years_experience" 
                                               value="<?php echo $doctorProfile['years_experience'] ?? ''; ?>" min="0">
                                    </div>
                                </div>
                                
                                <!-- Profile Image -->
                                <div class="mb-4">
                                    <label for="profile_image" class="form-label">Profile Image</label>
                                    <?php if ($doctorProfile && $doctorProfile['profile_image']): ?>
                                        <div class="mb-2">
                                            <img src="../<?php echo htmlspecialchars($doctorProfile['profile_image']); ?>" 
                                                 alt="Current Profile" class="img-thumbnail" style="max-width: 150px;">
                                        </div>
                                    <?php endif; ?>
                                    <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                    <div class="form-text">Upload a new image to replace the current one.</div>
                                </div>
                                
                                <!-- Bio -->
                                <div class="mb-4">
                                    <label for="bio" class="form-label">Biography</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4"><?php echo htmlspecialchars($doctorProfile['bio'] ?? ''); ?></textarea>
                                </div>
                                
                                <!-- Contact Information -->
                                <h6 class="text-primary mb-3">Contact Information</h6>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($doctorProfile['phone'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($doctorProfile['email'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="whatsapp" class="form-label">WhatsApp</label>
                                        <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                                               value="<?php echo htmlspecialchars($doctorProfile['whatsapp'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <!-- Clinic Information -->
                                <h6 class="text-primary mb-3">Clinic Information</h6>
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <label for="clinic_address" class="form-label">Clinic Address</label>
                                        <textarea class="form-control" id="clinic_address" name="clinic_address" rows="2"><?php echo htmlspecialchars($doctorProfile['clinic_address'] ?? ''); ?></textarea>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="consultation_fee" class="form-label">Consultation Fee (৳)</label>
                                        <input type="number" class="form-control" id="consultation_fee" name="consultation_fee" 
                                               value="<?php echo $doctorProfile['consultation_fee'] ?? ''; ?>" min="0" step="0.01">
                                    </div>
                                </div>
                                
                                <!-- Additional Information -->
                                <h6 class="text-primary mb-3">Additional Information</h6>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="languages" class="form-label">Languages Spoken</label>
                                        <input type="text" class="form-control" id="languages" name="languages" 
                                               value="<?php echo htmlspecialchars($doctorProfile['languages'] ?? ''); ?>" 
                                               placeholder="e.g., English, Bengali, Hindi">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="awards" class="form-label">Awards & Recognition</label>
                                        <textarea class="form-control" id="awards" name="awards" rows="2"><?php echo htmlspecialchars($doctorProfile['awards'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="certifications" class="form-label">Certifications</label>
                                    <textarea class="form-control" id="certifications" name="certifications" rows="2"><?php echo htmlspecialchars($doctorProfile['certifications'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>Save Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Preview -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">Profile Preview</h6>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($doctorProfile && $doctorProfile['profile_image']): ?>
                                <img src="../<?php echo htmlspecialchars($doctorProfile['profile_image']); ?>" 
                                     alt="Profile" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                            <?php else: ?>
                                <div class="bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user-md fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <h5><?php echo htmlspecialchars($doctorProfile['full_name'] ?? 'Doctor Name'); ?></h5>
                            <p class="text-muted"><?php echo htmlspecialchars($doctorProfile['specialization'] ?? 'Specialization'); ?></p>
                            
                            <?php if ($doctorProfile && $doctorProfile['years_experience']): ?>
                                <div class="badge bg-primary mb-2">
                                    <?php echo $doctorProfile['years_experience']; ?>+ Years Experience
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($doctorProfile && $doctorProfile['consultation_fee']): ?>
                                <div class="mt-3">
                                    <small class="text-muted">Consultation Fee</small>
                                    <div class="h5 text-success">৳<?php echo number_format($doctorProfile['consultation_fee']); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="update_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="8">
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
