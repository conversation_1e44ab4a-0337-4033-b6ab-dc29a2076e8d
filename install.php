<?php
/**
 * Installation Script for Doctor <PERSON>lio Website
 * Run this file once to set up the database and initial data
 */

// Include configuration
require_once 'config/database.php';

// Check if already installed
if (file_exists('installed.lock')) {
    die('<h1>Already Installed</h1><p>The application has already been installed. Delete the "installed.lock" file to reinstall.</p>');
}

$errors = [];
$success = [];

// Function to execute SQL file
function executeSQLFile($filename, $connection) {
    if (!file_exists($filename)) {
        return "File {$filename} not found.";
    }
    
    $sql = file_get_contents($filename);
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $connection->exec($statement);
            } catch (PDOException $e) {
                return "Error executing statement: " . $e->getMessage();
            }
        }
    }
    
    return true;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Create database connection
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Execute schema
        $result = executeSQLFile('Database/schema.sql', $pdo);
        if ($result === true) {
            $success[] = "Database schema created successfully.";
        } else {
            $errors[] = $result;
        }
        
        // Execute sample data
        if (empty($errors)) {
            $result = executeSQLFile('Database/sample_data.sql', $pdo);
            if ($result === true) {
                $success[] = "Sample data inserted successfully.";
            } else {
                $errors[] = $result;
            }
        }
        
        // Create admin user with custom password if provided
        if (empty($errors) && !empty($_POST['admin_password'])) {
            $adminPassword = password_hash($_POST['admin_password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
            $stmt->execute([$adminPassword]);
            $success[] = "Admin password updated successfully.";
        }
        
        // Create lock file if successful
        if (empty($errors)) {
            file_put_contents('installed.lock', date('Y-m-d H:i:s'));
            $success[] = "Installation completed successfully!";
        }
        
    } catch (PDOException $e) {
        $errors[] = "Database connection failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Doctor Portfolio Website</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .card { border: none; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .card-header { background: linear-gradient(135deg, #2c5aa0 0%, #17a2b8 100%); color: white; }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="card">
            <div class="card-header text-center">
                <h2 class="mb-0">Doctor Portfolio Website Installation</h2>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h5>Installation Errors:</h5>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <h5>Installation Success:</h5>
                        <ul class="mb-0">
                            <?php foreach ($success as $msg): ?>
                                <li><?php echo htmlspecialchars($msg); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <?php if (file_exists('installed.lock')): ?>
                        <div class="text-center mt-4">
                            <h4>Installation Complete!</h4>
                            <p>You can now access your website:</p>
                            <a href="index.php" class="btn btn-primary me-2">View Website</a>
                            <a href="admin/login.php" class="btn btn-success">Admin Login</a>
                            <hr>
                            <small class="text-muted">
                                <strong>Default Admin Credentials:</strong><br>
                                Username: admin<br>
                                Password: <?php echo !empty($_POST['admin_password']) ? 'Your custom password' : 'admin123'; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <form method="POST">
                        <div class="mb-4">
                            <h5>Database Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Host:</label>
                                    <input type="text" class="form-control" value="<?php echo DB_HOST; ?>" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Database:</label>
                                    <input type="text" class="form-control" value="<?php echo DB_NAME; ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5>Admin Account Setup</h5>
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">Admin Password (optional)</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" 
                                       placeholder="Leave blank to use default: admin123">
                                <div class="form-text">Choose a strong password for security.</div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5>Pre-Installation Checklist</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PHP Version (8.0+)
                                    <span class="badge <?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo PHP_VERSION; ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PDO MySQL Extension
                                    <span class="badge <?php echo extension_loaded('pdo_mysql') ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo extension_loaded('pdo_mysql') ? 'Available' : 'Missing'; ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    GD Extension (for images)
                                    <span class="badge <?php echo extension_loaded('gd') ? 'bg-success' : 'bg-warning'; ?>">
                                        <?php echo extension_loaded('gd') ? 'Available' : 'Missing'; ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Uploads Directory Writable
                                    <span class="badge <?php echo is_writable('.') ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo is_writable('.') ? 'Writable' : 'Not Writable'; ?>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Install Database & Setup
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
