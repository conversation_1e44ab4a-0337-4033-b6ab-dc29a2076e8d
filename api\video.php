<?php
/**
 * Video API Endpoint
 * Handles AJAX requests for video management
 */

require_once '../includes/init.php';
require_once '../models/Video.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is authenticated (for admin operations)
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$videoModel = new Video();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single video
                $video = $videoModel->getVideoById($_GET['id']);
                if ($video) {
                    echo json_encode(['success' => true, 'video' => $video]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Video not found']);
                }
            } else {
                // Get all videos
                $videos = $videoModel->getAllVideos();
                echo json_encode(['success' => true, 'videos' => $videos]);
            }
            break;
            
        case 'POST':
            // Create new video
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
                break;
            }
            
            // Validate required fields
            if (empty($input['title']) || empty($input['video_url'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Title and video URL are required']);
                break;
            }
            
            // Validate video URL
            if (!$videoModel->validateVideoUrl($input['video_url'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid video URL format']);
                break;
            }
            
            $data = [
                'title' => trim($input['title']),
                'description' => trim($input['description'] ?? ''),
                'video_url' => trim($input['video_url']),
                'category' => trim($input['category'] ?? ''),
                'tags' => trim($input['tags'] ?? ''),
                'duration' => !empty($input['duration']) ? intval($input['duration']) : null,
                'created_by' => $_SESSION['admin_id']
            ];
            
            // Auto-generate thumbnail for YouTube videos
            $youtubeId = $videoModel->extractYouTubeId($data['video_url']);
            if ($youtubeId) {
                $data['thumbnail'] = $videoModel->getYouTubeThumbnail($youtubeId);
            }
            
            if ($videoModel->createVideo($data)) {
                echo json_encode(['success' => true, 'message' => 'Video created successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to create video']);
            }
            break;
            
        case 'PUT':
            // Update video
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Video ID is required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
                break;
            }
            
            $data = [
                'title' => trim($input['title']),
                'description' => trim($input['description'] ?? ''),
                'video_url' => trim($input['video_url']),
                'category' => trim($input['category'] ?? ''),
                'tags' => trim($input['tags'] ?? ''),
                'duration' => !empty($input['duration']) ? intval($input['duration']) : null
            ];
            
            // Auto-generate thumbnail for YouTube videos
            $youtubeId = $videoModel->extractYouTubeId($data['video_url']);
            if ($youtubeId) {
                $data['thumbnail'] = $videoModel->getYouTubeThumbnail($youtubeId);
            }
            
            if ($videoModel->updateVideo($_GET['id'], $data)) {
                echo json_encode(['success' => true, 'message' => 'Video updated successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to update video']);
            }
            break;
            
        case 'DELETE':
            // Delete video
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Video ID is required']);
                break;
            }
            
            if ($videoModel->deleteVideo($_GET['id'])) {
                echo json_encode(['success' => true, 'message' => 'Video deleted successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to delete video']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
