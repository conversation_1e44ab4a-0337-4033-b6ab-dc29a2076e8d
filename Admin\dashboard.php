<?php
require_once '../includes/init.php';
require_once '../models/User.php';
require_once '../models/Appointment.php';
require_once '../models/DoctorProfile.php';

$pageTitle = 'Dashboard';

// Get statistics
$appointmentModel = new Appointment();
$stats = $appointmentModel->getAppointmentStats();

// Get recent appointments
$recentAppointments = $appointmentModel->getUpcomingAppointments(5);

// Get unread messages count
try {
    $db = Database::getInstance();
    $unreadMessages = $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE is_read = 0");
    $stats['unread_messages'] = $unreadMessages['count'];
} catch (Exception $e) {
    $stats['unread_messages'] = 0;
}

// Get doctor profile
$doctorProfileModel = new DoctorProfile();
$doctorProfile = $doctorProfileModel->getActiveProfile();
?>

<?php include 'includes/header.php'; ?>

<!-- Dashboard Content -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Dashboard</h1>
            <div class="d-flex gap-2">
                <a href="../index.php" class="btn btn-outline-primary" target="_blank">
                    <i class="bi bi-eye me-2"></i>View Website
                </a>
                <a href="appointments.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Manage Appointments
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="number"><?php echo $stats['total']; ?></div>
                    <div>Total Appointments</div>
                </div>
                <div class="icon">
                    <i class="bi bi-calendar-check"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="number"><?php echo $stats['pending']; ?></div>
                    <div>Pending Appointments</div>
                </div>
                <div class="icon">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="number"><?php echo $stats['today']; ?></div>
                    <div>Today's Appointments</div>
                </div>
                <div class="icon">
                    <i class="bi bi-calendar-day"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="number"><?php echo $stats['unread_messages']; ?></div>
                    <div>Unread Messages</div>
                </div>
                <div class="icon">
                    <i class="bi bi-envelope"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Appointments -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>Recent Appointments</h5>
                <a href="appointments.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentAppointments)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Patient</th>
                                <th>Date & Time</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentAppointments as $appointment): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($appointment['patient_name']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($appointment['patient_phone']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <?php echo formatDate($appointment['appointment_date']); ?>
                                        <br>
                                        <small class="text-muted"><?php echo formatTime($appointment['appointment_time']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge status-<?php echo $appointment['status']; ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="appointments.php?action=view&id=<?php echo $appointment['id']; ?>" 
                                           class="btn btn-outline-primary" title="View">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <?php if ($appointment['status'] === 'pending'): ?>
                                        <a href="appointments.php?action=approve&id=<?php echo $appointment['id']; ?>" 
                                           class="btn btn-outline-success" title="Approve">
                                            <i class="bi bi-check"></i>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
                    <p class="text-muted">No upcoming appointments</p>
                    <a href="appointments.php" class="btn btn-primary">Manage Appointments</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions & Profile Summary -->
    <div class="col-lg-4 mb-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="appointments.php" class="btn btn-primary">
                        <i class="bi bi-calendar-check me-2"></i>Manage Appointments
                    </a>
                    <a href="profile.php" class="btn btn-outline-primary">
                        <i class="bi bi-person-circle me-2"></i>Update Profile
                    </a>
                    <a href="messages.php" class="btn btn-outline-primary">
                        <i class="bi bi-envelope me-2"></i>View Messages
                        <?php if ($stats['unread_messages'] > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $stats['unread_messages']; ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="videos.php" class="btn btn-outline-primary">
                        <i class="bi bi-play-circle me-2"></i>Manage Videos
                    </a>
                    <a href="articles.php" class="btn btn-outline-primary">
                        <i class="bi bi-file-text me-2"></i>Manage Articles
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Profile Summary -->
        <?php if ($doctorProfile): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Profile Summary</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <?php if ($doctorProfile['profile_photo']): ?>
                        <img src="../<?php echo htmlspecialchars($doctorProfile['profile_photo']); ?>" 
                             alt="Profile Photo" class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 80px; height: 80px;">
                            <i class="bi bi-person-fill fs-2"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="text-center">
                    <h6 class="mb-1"><?php echo htmlspecialchars($doctorProfile['full_name']); ?></h6>
                    <p class="text-muted small mb-2"><?php echo htmlspecialchars($doctorProfile['specialization']); ?></p>
                    <p class="small mb-3"><?php echo $doctorProfile['years_experience']; ?> years of experience</p>
                    
                    <div class="d-grid">
                        <a href="profile.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Monthly Appointments Chart (Placeholder) -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Monthly Appointments Overview</h5>
            </div>
            <div class="card-body">
                <div class="text-center py-5">
                    <i class="bi bi-graph-up fs-1 text-muted mb-3"></i>
                    <h6 class="text-muted">Appointment Analytics</h6>
                    <p class="text-muted">Chart functionality can be implemented with Chart.js or similar library</p>
                    <small class="text-muted">This month: <?php echo $stats['this_month']; ?> appointments</small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
