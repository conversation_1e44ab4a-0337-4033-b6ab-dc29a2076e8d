<?php
/**
 * Article Model
 * Handles blog articles and health content management
 */

class Article {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get all articles
     */
    public function getAllArticles($limit = null, $offset = 0) {
        $sql = "SELECT a.*, u.full_name as author_name 
                FROM articles a 
                LEFT JOIN users u ON a.author_id = u.id 
                ORDER BY a.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get published articles
     */
    public function getPublishedArticles($limit = null, $offset = 0) {
        $sql = "SELECT a.*, u.full_name as author_name
                FROM articles a
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.is_published = 1
                ORDER BY a.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get article by ID
     */
    public function getArticleById($id) {
        $sql = "SELECT a.*, u.full_name as author_name
                FROM articles a
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get article by slug
     */
    public function getArticleBySlug($slug) {
        $sql = "SELECT a.*, u.full_name as author_name
                FROM articles a
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.slug = ? AND a.is_published = 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$slug]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get published articles by category
     */
    public function getPublishedArticlesByCategory($category, $limit = null, $offset = 0) {
        $sql = "SELECT a.*, u.full_name as author_name
                FROM articles a
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.is_published = 1 AND a.category = ?
                ORDER BY a.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get published articles count
     */
    public function getPublishedArticlesCount() {
        $sql = "SELECT COUNT(*) as count FROM articles WHERE is_published = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    /**
     * Get published articles count by category
     */
    public function getPublishedArticlesCountByCategory($category) {
        $sql = "SELECT COUNT(*) as count FROM articles WHERE is_published = 1 AND category = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }

    /**
     * Search published articles
     */
    public function searchPublishedArticles($search, $limit = null, $offset = 0) {
        $sql = "SELECT a.*, u.full_name as author_name
                FROM articles a
                LEFT JOIN users u ON a.created_by = u.id
                WHERE a.is_published = 1
                AND (a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ? OR a.tags LIKE ?)
                ORDER BY a.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }

        $searchTerm = '%' . $search . '%';
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get search results count
     */
    public function getSearchResultsCount($search) {
        $sql = "SELECT COUNT(*) as count FROM articles
                WHERE is_published = 1
                AND (title LIKE ? OR content LIKE ? OR excerpt LIKE ? OR tags LIKE ?)";

        $searchTerm = '%' . $search . '%';
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }
    
    /**
     * Create new article
     */
    public function createArticle($data) {
        $isPublished = ($data['status'] ?? 'draft') === 'published' ? 1 : 0;
        $sql = "INSERT INTO articles (title, slug, content, excerpt, featured_image, category, tags, is_published, created_by, meta_description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['title'],
            $data['slug'],
            $data['content'],
            $data['excerpt'] ?? '',
            $data['featured_image'] ?? null,
            $data['category'] ?? '',
            $data['tags'] ?? '',
            $isPublished,
            $data['author_id'] ?? $data['created_by'],
            $data['meta_description'] ?? $data['excerpt']
        ]);
    }
    
    /**
     * Update article
     */
    public function updateArticle($id, $data) {
        $isPublished = ($data['status'] ?? 'draft') === 'published' ? 1 : 0;
        $sql = "UPDATE articles SET
                title = ?, slug = ?, content = ?, excerpt = ?, featured_image = ?,
                category = ?, tags = ?, is_published = ?, meta_description = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['title'],
            $data['slug'],
            $data['content'],
            $data['excerpt'] ?? '',
            $data['featured_image'] ?? null,
            $data['category'] ?? '',
            $data['tags'] ?? '',
            $isPublished,
            $data['meta_description'] ?? $data['excerpt'],
            $id
        ]);
    }
    
    /**
     * Delete article
     */
    public function deleteArticle($id) {
        $sql = "DELETE FROM articles WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    /**
     * Get articles by category
     */
    public function getArticlesByCategory($category, $limit = null) {
        $sql = "SELECT a.*, u.full_name as author_name 
                FROM articles a 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE a.category = ? AND a.status = 'published' 
                ORDER BY a.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get article categories
     */
    public function getCategories() {
        $sql = "SELECT DISTINCT category FROM articles WHERE category IS NOT NULL AND category != '' ORDER BY category";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Generate unique slug
     */
    public function generateSlug($title, $id = null) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $id)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Check if slug exists
     */
    private function slugExists($slug, $excludeId = null) {
        $sql = "SELECT id FROM articles WHERE slug = ?";
        $params = [$slug];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Get article statistics
     */
    public function getArticleStats() {
        $sql = "SELECT
                    COUNT(*) as total_articles,
                    COUNT(CASE WHEN is_published = 1 THEN 1 END) as published_articles,
                    COUNT(CASE WHEN is_published = 0 THEN 1 END) as draft_articles,
                    COUNT(DISTINCT category) as total_categories
                FROM articles";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search articles
     */
    public function searchArticles($query, $limit = 10) {
        $sql = "SELECT a.*, u.full_name as author_name 
                FROM articles a 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE (a.title LIKE ? OR a.content LIKE ? OR a.tags LIKE ?) 
                AND a.status = 'published' 
                ORDER BY a.created_at DESC 
                LIMIT " . intval($limit);
        
        $searchTerm = '%' . $query . '%';
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get recent articles
     */
    public function getRecentArticles($limit = 5) {
        $sql = "SELECT a.*, u.full_name as author_name 
                FROM articles a 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE a.status = 'published' 
                ORDER BY a.created_at DESC 
                LIMIT " . intval($limit);
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
