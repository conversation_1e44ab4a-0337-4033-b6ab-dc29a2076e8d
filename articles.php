<?php
require_once 'includes/init.php';
require_once 'models/Article.php';
require_once 'models/DoctorProfile.php';

$pageTitle = 'Health Articles & Blog';
$articleModel = new Article();
$doctorProfileModel = new DoctorProfile();
$doctorProfile = $doctorProfileModel->getActiveProfile();

// Get filter parameters
$category = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 9;
$offset = ($page - 1) * $perPage;

// Get published articles based on filters
if ($category) {
    $articles = $articleModel->getPublishedArticlesByCategory($category, $perPage, $offset);
    $totalArticles = $articleModel->getPublishedArticlesCountByCategory($category);
} else {
    $articles = $articleModel->getPublishedArticles($perPage, $offset);
    $totalArticles = $articleModel->getPublishedArticlesCount();
}

// Filter by search term if provided
if ($search) {
    $articles = $articleModel->searchPublishedArticles($search, $perPage, $offset);
    $totalArticles = $articleModel->getSearchResultsCount($search);
}

// Calculate pagination
$totalPages = ceil($totalArticles / $perPage);

// Get all categories for filter dropdown
$categories = $articleModel->getCategories();

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="mb-4">
                    <i class="fas fa-newspaper display-1 mb-3 opacity-75"></i>
                </div>
                <h1 class="display-4 fw-bold mb-3">Health Articles & <span class="gradient-text">Blog</span></h1>
                <p class="lead mb-4">Stay informed with our latest health articles, medical insights, and wellness tips</p>
            </div>
        </div>
    </div>
</section>

<!-- Article Filters -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <form method="GET" class="d-flex flex-wrap gap-3 align-items-center">
                    <div class="flex-grow-1">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Search articles..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <select name="category" class="form-select">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat); ?>" 
                                        <?php echo $category === $cat ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="flex-shrink-0">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                    
                    <?php if ($category || $search): ?>
                    <div class="flex-shrink-0">
                        <a href="articles.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Articles Grid -->
<section class="py-5" id="main-content">
    <div class="container">
        <?php if (empty($articles)): ?>
            <div class="text-center py-5" data-aos="fade-up">
                <i class="fas fa-newspaper fa-4x text-muted mb-4"></i>
                <h3>No Articles Found</h3>
                <p class="text-muted mb-4">
                    <?php if ($search || $category): ?>
                        No articles match your current filters. Try adjusting your search criteria.
                    <?php else: ?>
                        We're working on adding health articles. Please check back soon!
                    <?php endif; ?>
                </p>
                <a href="articles.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>View All Articles
                </a>
            </div>
        <?php else: ?>
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <?php echo $totalArticles; ?> Article<?php echo $totalArticles !== 1 ? 's' : ''; ?> Found
                            <?php if ($category): ?>
                                <span class="badge bg-primary ms-2"><?php echo htmlspecialchars($category); ?></span>
                            <?php endif; ?>
                        </h4>
                        
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sort me-2"></i>Sort By
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'newest'])); ?>">Newest First</a></li>
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'oldest'])); ?>">Oldest First</a></li>
                                <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['sort' => 'title'])); ?>">Title A-Z</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Articles Grid -->
            <div class="row">
                <?php foreach ($articles as $index => $article): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index % 6) * 100; ?>">
                    <div class="article-card card h-100 border-0 shadow-sm">
                        <?php if ($article['featured_image']): ?>
                        <div class="article-image">
                            <img src="<?php echo htmlspecialchars($article['featured_image']); ?>" 
                                 alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                 class="card-img-top"
                                 style="height: 200px; object-fit: cover;">
                        </div>
                        <?php endif; ?>
                        
                        <div class="card-body">
                            <?php if ($article['category']): ?>
                            <div class="article-category mb-2">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($article['category']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <h5 class="card-title">
                                <a href="article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>" 
                                   class="text-decoration-none">
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </a>
                            </h5>
                            
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars($article['excerpt'] ?: substr(strip_tags($article['content']), 0, 150)); ?>
                                <?php if (strlen($article['excerpt'] ?: $article['content']) > 150): ?>...<?php endif; ?>
                            </p>
                            
                            <?php if ($article['tags']): ?>
                            <div class="article-tags mb-3">
                                <?php 
                                $tags = explode(',', $article['tags']);
                                foreach (array_slice($tags, 0, 3) as $tag): 
                                ?>
                                <span class="badge bg-light text-dark me-1"><?php echo htmlspecialchars(trim($tag)); ?></span>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-transparent border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('M j, Y', strtotime($article['published_at'] ?: $article['created_at'])); ?>
                                </small>
                                <a href="article.php?slug=<?php echo htmlspecialchars($article['slug']); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    Read More <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="row mt-5">
                <div class="col-12">
                    <nav aria-label="Articles pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
