<?php
/**
 * Message API Endpoint
 * Handles AJAX requests for message management
 */

require_once '../includes/init.php';
require_once '../models/Message.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is authenticated (for admin operations)
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$messageModel = new Message();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single message
                $message = $messageModel->getMessageById($_GET['id']);
                if ($message) {
                    echo json_encode(['success' => true, 'message' => $message]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Message not found']);
                }
            } elseif (isset($_GET['status'])) {
                // Get messages by status
                $messages = $messageModel->getMessagesByStatus($_GET['status']);
                echo json_encode(['success' => true, 'messages' => $messages]);
            } elseif (isset($_GET['search'])) {
                // Search messages
                $messages = $messageModel->searchMessages($_GET['search']);
                echo json_encode(['success' => true, 'messages' => $messages]);
            } else {
                // Get all messages
                $messages = $messageModel->getAllMessages();
                echo json_encode(['success' => true, 'messages' => $messages]);
            }
            break;
            
        case 'POST':
            // Create new message (public endpoint)
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
                break;
            }
            
            // Validate required fields
            if (empty($input['name']) || empty($input['email']) || empty($input['message'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Name, email, and message are required']);
                break;
            }
            
            $data = [
                'name' => trim($input['name']),
                'email' => trim($input['email']),
                'phone' => trim($input['phone'] ?? ''),
                'subject' => trim($input['subject'] ?? ''),
                'message' => trim($input['message'])
            ];
            
            if ($messageModel->createMessage($data)) {
                echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to send message']);
            }
            break;
            
        case 'PUT':
            // Update message status
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message ID is required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['status'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Status is required']);
                break;
            }
            
            $validStatuses = ['unread', 'read', 'replied'];
            if (!in_array($input['status'], $validStatuses)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid status']);
                break;
            }
            
            if ($messageModel->updateMessageStatus($_GET['id'], $input['status'])) {
                echo json_encode(['success' => true, 'message' => 'Message status updated successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to update message status']);
            }
            break;
            
        case 'DELETE':
            // Delete message
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message ID is required']);
                break;
            }
            
            if ($messageModel->deleteMessage($_GET['id'])) {
                echo json_encode(['success' => true, 'message' => 'Message deleted successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to delete message']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
