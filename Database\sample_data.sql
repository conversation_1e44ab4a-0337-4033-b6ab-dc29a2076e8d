-- Sample Data for <PERSON><PERSON> Website
-- Insert after running schema.sql

USE doctor_portfolio;

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. Admin', 'admin');

-- Insert doctor profile
INSERT INTO doctor_profile (
    full_name, 
    medical_degrees, 
    specialization, 
    years_experience, 
    clinic_address, 
    phone, 
    email, 
    whatsapp, 
    biography,
    work_schedule,
    consultation_fee
) VALUES (
    'Dr. <PERSON>',
    'MBBS, MD (Cardiology), FCPS',
    'Cardiologist & Heart Specialist',
    15,
    'Rahman Heart Center\n123 Medical Street, Dhanmondi\nDhaka-1205, Bangladesh',
    '+880-1712-345678',
    '<EMAIL>',
    '+880-1712-345678',
    'Dr. <PERSON> is a highly experienced cardiologist with over 15 years of practice in cardiovascular medicine. He completed his MBBS from Dhaka Medical College and pursued his MD in Cardiology from Bangabandhu Sheikh <PERSON> Medical University. Dr. <PERSON> specializes in interventional cardiology, heart disease prevention, and cardiac rehabilitation. He has performed over 2000 successful cardiac procedures and is known for his patient-centered approach to healthcare.',
    '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "14:00"}, "saturday": {"start": "10:00", "end": "16:00"}, "sunday": "closed"}',
    1500.00
);

-- Insert certifications
INSERT INTO certifications (doctor_id, title, issuing_organization, issue_date) VALUES
(1, 'Board Certification in Cardiology', 'Bangladesh College of Physicians and Surgeons', '2015-06-15'),
(1, 'Advanced Cardiac Life Support (ACLS)', 'American Heart Association', '2023-03-20'),
(1, 'Interventional Cardiology Fellowship', 'National Heart Foundation', '2016-12-10');

-- Insert awards
INSERT INTO awards (doctor_id, title, awarding_organization, award_date, description) VALUES
(1, 'Best Cardiologist Award 2023', 'Bangladesh Medical Association', '2023-12-01', 'Recognized for outstanding contribution to cardiovascular medicine'),
(1, 'Excellence in Patient Care', 'Dhaka Medical Society', '2022-08-15', 'Awarded for exceptional patient care and treatment outcomes');

-- Insert sample appointments
INSERT INTO appointments (patient_name, patient_phone, patient_email, appointment_date, appointment_time, reason_for_visit, status) VALUES
('Ahmed Hassan', '+880-1711-111111', '<EMAIL>', '2025-07-25', '10:00:00', 'Chest pain and shortness of breath', 'pending'),
('Fatima Khatun', '+880-1722-222222', '<EMAIL>', '2025-07-26', '14:30:00', 'Regular cardiac checkup', 'approved'),
('Karim Uddin', '+880-1733-333333', '<EMAIL>', '2025-07-24', '11:00:00', 'Follow-up after angioplasty', 'completed');

-- Insert sample contact messages
INSERT INTO contact_messages (name, email, phone, subject, message) VALUES
('Sarah Ahmed', '<EMAIL>', '+880-1744-444444', 'Appointment Inquiry', 'I would like to schedule an appointment for my father who has been experiencing chest pain.'),
('Rafiq Islam', '<EMAIL>', '+880-1755-555555', 'Insurance Query', 'Do you accept XYZ insurance for cardiac procedures?');

-- Insert settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_title', 'Dr. Mohammad Rahman - Cardiologist', 'text', 'Website title'),
('site_description', 'Leading cardiologist in Dhaka providing comprehensive heart care services', 'text', 'Website description'),
('appointment_slots', '["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30"]', 'json', 'Available appointment time slots'),
('google_maps_embed', '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3651.9" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>', 'text', 'Google Maps embed code'),
('whatsapp_message', 'Hello Dr. Rahman, I would like to schedule an appointment.', 'text', 'Default WhatsApp message');

-- Insert sample videos (admin content only)
INSERT INTO videos (title, description, video_url, category, tags, created_by) VALUES
('Heart Health Tips', 'Essential tips for maintaining a healthy heart', 'https://youtube.com/watch?v=example1', 'Health Education', 'heart, health, prevention', 1),
('Understanding Hypertension', 'Complete guide to high blood pressure management', 'https://youtube.com/watch?v=example2', 'Medical Education', 'hypertension, blood pressure, treatment', 1);

-- Insert sample articles (admin content only)
INSERT INTO articles (title, slug, content, excerpt, category, tags, is_published, published_at, created_by) VALUES
('10 Ways to Keep Your Heart Healthy', '10-ways-keep-heart-healthy', 
'<h2>Introduction</h2><p>Heart health is crucial for overall well-being...</p><h3>1. Regular Exercise</h3><p>Engage in at least 30 minutes of moderate exercise daily...</p>', 
'Discover the top 10 evidence-based strategies to maintain optimal heart health and prevent cardiovascular disease.',
'Health Tips', 'heart health, prevention, lifestyle', TRUE, NOW(), 1),

('Understanding Cardiac Symptoms', 'understanding-cardiac-symptoms',
'<h2>Common Heart Disease Symptoms</h2><p>Recognizing the early signs of heart problems...</p>',
'Learn to identify important cardiac symptoms that require immediate medical attention.',
'Medical Information', 'symptoms, heart disease, diagnosis', TRUE, NOW(), 1);
