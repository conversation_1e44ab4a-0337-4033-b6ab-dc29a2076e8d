<?php
require_once '../includes/init.php';
require_once '../models/Article.php';
require_once '../models/User.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'Article Management';
$articleModel = new Article();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!Security::validateCSRFToken($_POST['csrf_token'])) {
        $_SESSION['flash_message'] = 'Invalid security token.';
        $_SESSION['flash_type'] = 'danger';
        header('Location: articles.php');
        exit;
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create' || $action === 'update') {
        $data = [
            'title' => trim($_POST['title']),
            'content' => trim($_POST['content']),
            'excerpt' => trim($_POST['excerpt']),
            'category' => trim($_POST['category']),
            'tags' => trim($_POST['tags']),
            'status' => $_POST['status'] ?? 'draft',
            'meta_title' => trim($_POST['meta_title']),
            'meta_description' => trim($_POST['meta_description']),
            'author_id' => $_SESSION['admin_id']
        ];
        
        // Generate slug
        $data['slug'] = $articleModel->generateSlug($data['title'], $action === 'update' ? intval($_POST['article_id']) : null);
        
        // Handle featured image upload
        if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/articles/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $fileName = time() . '_' . $_FILES['featured_image']['name'];
            $uploadPath = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['featured_image']['tmp_name'], $uploadPath)) {
                $data['featured_image'] = 'uploads/articles/' . $fileName;
            }
        }
        
        if ($action === 'create') {
            if ($articleModel->createArticle($data)) {
                $_SESSION['flash_message'] = 'Article created successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to create article.';
                $_SESSION['flash_type'] = 'danger';
            }
        } else {
            $articleId = intval($_POST['article_id']);
            if ($articleModel->updateArticle($articleId, $data)) {
                $_SESSION['flash_message'] = 'Article updated successfully.';
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to update article.';
                $_SESSION['flash_type'] = 'danger';
            }
        }
    } elseif ($action === 'delete') {
        $articleId = intval($_POST['article_id']);
        if ($articleModel->deleteArticle($articleId)) {
            $_SESSION['flash_message'] = 'Article deleted successfully.';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'Failed to delete article.';
            $_SESSION['flash_type'] = 'danger';
        }
    }
    
    header('Location: articles.php');
    exit;
}

// Get articles and statistics
$articles = $articleModel->getAllArticles();
$categories = $articleModel->getCategories();
$stats = $articleModel->getArticleStats();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-newspaper me-2"></i>Article Management</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#articleModal">
                    <i class="fas fa-plus me-2"></i>Add New Article
                </button>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_articles']; ?></h4>
                                    <p class="mb-0">Total Articles</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-newspaper fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['published_articles']; ?></h4>
                                    <p class="mb-0">Published</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['draft_articles']; ?></h4>
                                    <p class="mb-0">Drafts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-edit fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_categories']; ?></h4>
                                    <p class="mb-0">Categories</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-tags fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Articles Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Articles</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($articles)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h5>No articles found</h5>
                            <p class="text-muted">Start by creating your first article.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Author</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($articles as $article): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($article['title']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($article['excerpt'], 0, 100)); ?>...</small>
                                        </td>
                                        <td>
                                            <?php if ($article['category']): ?>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($article['category']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($article['status'] === 'published'): ?>
                                                <span class="badge bg-success">Published</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Draft</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($article['author_name'] ?? 'Unknown'); ?></td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($article['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editArticle(<?php echo $article['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteArticle(<?php echo $article['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Article Modal -->
<div class="modal fade" id="articleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="articleModalTitle">Add New Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="articleForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="create">
                    <input type="hidden" name="article_id" value="">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="excerpt" class="form-label">Excerpt</label>
                                <textarea class="form-control" id="excerpt" name="excerpt" rows="2" placeholder="Brief description of the article"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="content" class="form-label">Content *</label>
                                <textarea class="form-control" id="content" name="content" rows="10" required></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <input type="text" class="form-control" id="category" name="category" list="categoryList">
                                <datalist id="categoryList">
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags" placeholder="tag1, tag2, tag3">
                            </div>
                            
                            <div class="mb-3">
                                <label for="featured_image" class="form-label">Featured Image</label>
                                <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                            </div>
                            
                            <div class="mb-3">
                                <label for="meta_title" class="form-label">Meta Title</label>
                                <input type="text" class="form-control" id="meta_title" name="meta_title">
                            </div>
                            
                            <div class="mb-3">
                                <label for="meta_description" class="form-label">Meta Description</label>
                                <textarea class="form-control" id="meta_description" name="meta_description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Article</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Article management JavaScript
function editArticle(id) {
    // Fetch article data and populate modal
    fetch(`../api/article.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const article = data.article;
                document.getElementById('articleModalTitle').textContent = 'Edit Article';
                document.querySelector('input[name="action"]').value = 'update';
                document.querySelector('input[name="article_id"]').value = article.id;
                document.getElementById('title').value = article.title;
                document.getElementById('excerpt').value = article.excerpt || '';
                document.getElementById('content').value = article.content || '';
                document.getElementById('category').value = article.category || '';
                document.getElementById('tags').value = article.tags || '';
                document.getElementById('status').value = article.status || 'draft';
                document.getElementById('meta_title').value = article.meta_title || '';
                document.getElementById('meta_description').value = article.meta_description || '';
                
                new bootstrap.Modal(document.getElementById('articleModal')).show();
            }
        });
}

function deleteArticle(id) {
    if (confirm('Are you sure you want to delete this article?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo Security::generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="article_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset modal when closed
document.getElementById('articleModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('articleForm').reset();
    document.getElementById('articleModalTitle').textContent = 'Add New Article';
    document.querySelector('input[name="action"]').value = 'create';
    document.querySelector('input[name="article_id"]').value = '';
});
</script>

<?php include 'includes/footer.php'; ?>
